const express = require('express');
const router = express.Router();
const Product = require('../models/Product');
const { protect, admin } = require('../middleware/auth');
const { upload, uploadToBlob } = require('../middleware/upload');
const fs = require('fs');
const path = require('path');

// Include review routes
const reviewRouter = require('./reviews');

// Re-route into review router
router.use('/:productId/reviews', reviewRouter);

/**
 * @route   GET /api/products
 * @desc    Get all products
 * @access  Public
 */
router.get('/', async (req, res) => {
  try {
    const { category, featured, search, sort, limit = 20, page = 1 } = req.query;
    const query = {};
    
    // Filter by category
    if (category) {
      query.category = category;
    }
    
    // Filter by featured
    if (featured === 'true') {
      query.featured = true;
    }
    
    // Search by name or description
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }
    
    // Sorting
    let sortOption = {};
    if (sort) {
      const sortFields = sort.split(',');
      sortFields.forEach(field => {
        if (field.startsWith('-')) {
          sortOption[field.substring(1)] = -1;
        } else {
          sortOption[field] = 1;
        }
      });
    } else {
      sortOption = { createdAt: -1 };
    }
    
    // Pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    
    const products = await Product.find(query)
      .sort(sortOption)
      .limit(parseInt(limit))
      .skip(skip);

    // Add pricing information to each product
    const productsWithPricing = products.map(product => {
      const productObj = product.toObject();
      productObj.pricingInfo = product.getPricingInfo();
      return productObj;
    });

    const total = await Product.countDocuments(query);

    res.json({
      products: productsWithPricing,
      page: parseInt(page),
      pages: Math.ceil(total / parseInt(limit)),
      total
    });
  } catch (error) {
    console.error('Error fetching products:', error);
    res.status(500).json({ error: error.message });
  }
});

/**
 * @route   GET /api/products/:id
 * @desc    Get single product
 * @access  Public
 */
router.get('/:id', async (req, res) => {
  try {
    const product = await Product.findById(req.params.id);

    if (product) {
      const productObj = product.toObject();
      productObj.pricingInfo = product.getPricingInfo();
      res.json(productObj);
    } else {
      res.status(404).json({ error: 'Product not found' });
    }
  } catch (error) {
    console.error('Error fetching product:', error);
    res.status(500).json({ error: error.message });
  }
});

/**
 * @route   POST /api/products
 * @desc    Create a product
 * @access  Private/Admin
 */
router.post('/', protect, admin, upload.single('image'), async (req, res) => {
  try {
    const {
      name,
      price,
      originalPrice,
      description,
      category,
      brand,
      inStock,
      featured,
      quantity,
      // Discount fields
      discountType,
      discountValue,
      discountStartDate,
      discountEndDate,
      // Tax fields
      taxRate,
      taxIncluded
    } = req.body;

    // Check if image was uploaded
    if (!req.file) {
      return res.status(400).json({ error: 'Please upload an image' });
    }

    // Upload image to Vercel Blob
    const filename = `${Date.now()}-${req.file.originalname.replace(/\s+/g, '-')}`;
    const imageUrl = await uploadToBlob(req.file, filename);

    const product = new Product({
      name,
      price: parseFloat(price),
      originalPrice: originalPrice ? parseFloat(originalPrice) : undefined,
      image: imageUrl,
      description,
      category,
      brand: brand || 'Unknown',
      inStock: inStock === 'true',
      featured: featured === 'true',
      quantity: parseInt(quantity || 0),
      createdBy: req.user._id,
      // Discount fields
      discountType: discountType || 'none',
      discountValue: discountValue ? parseFloat(discountValue) : 0,
      discountStartDate: discountStartDate ? new Date(discountStartDate) : undefined,
      discountEndDate: discountEndDate ? new Date(discountEndDate) : undefined,
      // Tax fields
      taxRate: taxRate ? parseFloat(taxRate) : 0,
      taxIncluded: taxIncluded === 'true'
    });

    const createdProduct = await product.save();
    res.status(201).json(createdProduct);
  } catch (error) {
    console.error('Error creating product:', error);
    res.status(500).json({ error: error.message });
  }
});

/**
 * @route   POST /api/products/:id/images
 * @desc    Add additional product images
 * @access  Private/Admin
 */
router.post('/:id/images', protect, admin, upload.array('images', 5), async (req, res) => {
  try {
    const product = await Product.findById(req.params.id);

    if (!product) {
      return res.status(404).json({ error: 'Product not found' });
    }

    // Check if images were uploaded
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({ error: 'Please upload at least one image' });
    }

    // Upload images to Vercel Blob
    const imageUrls = await Promise.all(
      req.files.map(async (file) => {
        const filename = `${Date.now()}-${file.originalname.replace(/\s+/g, '-')}`;
        return await uploadToBlob(file, filename);
      })
    );

    // Add new images to the product
    product.images = [...(product.images || []), ...imageUrls];

    const updatedProduct = await product.save();
    res.json(updatedProduct);
  } catch (error) {
    console.error('Error adding product images:', error);
    res.status(500).json({ error: error.message });
  }
});

/**
 * @route   PUT /api/products/:id
 * @desc    Update a product
 * @access  Private/Admin
 */
router.put('/:id', protect, admin, upload.single('image'), async (req, res) => {
  try {
    const product = await Product.findById(req.params.id);
    
    if (!product) {
      return res.status(404).json({ error: 'Product not found' });
    }
    
    const {
      name,
      price,
      originalPrice,
      description,
      category,
      inStock,
      featured,
      quantity,
      // Discount fields
      discountType,
      discountValue,
      discountStartDate,
      discountEndDate,
      // Tax fields
      taxRate,
      taxIncluded
    } = req.body;
    
    // Update image if a new one was uploaded
    let imageUrl = product.image;
    if (req.file) {
      // Upload new image to Vercel Blob
      const filename = `${Date.now()}-${req.file.originalname.replace(/\s+/g, '-')}`;
      imageUrl = await uploadToBlob(req.file, filename);

      // Note: With Vercel Blob, we don't need to delete old images manually
      // as they are managed by Vercel's infrastructure
    }
    
    product.name = name || product.name;
    product.price = price ? parseFloat(price) : product.price;
    product.originalPrice = originalPrice ? parseFloat(originalPrice) : product.originalPrice;
    product.image = imageUrl;
    product.description = description || product.description;
    product.category = category || product.category;
    product.inStock = inStock !== undefined ? inStock === 'true' : product.inStock;
    product.featured = featured !== undefined ? featured === 'true' : product.featured;
    product.quantity = quantity ? parseInt(quantity) : product.quantity;

    // Update discount fields
    product.discountType = discountType || product.discountType;
    product.discountValue = discountValue !== undefined ? parseFloat(discountValue) : product.discountValue;
    product.discountStartDate = discountStartDate ? new Date(discountStartDate) : product.discountStartDate;
    product.discountEndDate = discountEndDate ? new Date(discountEndDate) : product.discountEndDate;

    // Update tax fields
    product.taxRate = taxRate !== undefined ? parseFloat(taxRate) : product.taxRate;
    product.taxIncluded = taxIncluded !== undefined ? taxIncluded === 'true' : product.taxIncluded;
    
    const updatedProduct = await product.save();
    res.json(updatedProduct);
  } catch (error) {
    console.error('Error updating product:', error);
    res.status(500).json({ error: error.message });
  }
});

/**
 * @route   DELETE /api/products/:id
 * @desc    Delete a product
 * @access  Private/Admin
 */
router.delete('/:id', protect, admin, async (req, res) => {
  try {
    const product = await Product.findById(req.params.id);
    
    if (!product) {
      return res.status(404).json({ error: 'Product not found' });
    }
    
    // Delete product image if it's not a URL
    if (product.image && !product.image.startsWith('http') && fs.existsSync(path.join(__dirname, '..', product.image))) {
      fs.unlinkSync(path.join(__dirname, '..', product.image));
    }
    
    // Delete additional images if they exist
    if (product.images && product.images.length > 0) {
      product.images.forEach(img => {
        if (!img.startsWith('http') && fs.existsSync(path.join(__dirname, '..', img))) {
          fs.unlinkSync(path.join(__dirname, '..', img));
        }
      });
    }
    
    await product.deleteOne();
    res.json({ message: 'Product removed' });
  } catch (error) {
    console.error('Error deleting product:', error);
    res.status(500).json({ error: error.message });
  }
});

/**
 * @route   GET /api/products/categories
 * @desc    Get all product categories
 * @access  Public
 */
router.get('/categories/all', async (req, res) => {
  try {
    const categories = await Product.distinct('category');
    res.json(categories);
  } catch (error) {
    console.error('Error fetching categories:', error);
    res.status(500).json({ error: error.message });
  }
});

module.exports = router;