const Review = require('../models/Review');
const Product = require('../models/Product');
const asyncHandler = require('../middleware/asyncHandler');
const ErrorResponse = require('../utils/errorResponse');

// @desc    Get all reviews
// @route   GET /api/reviews
// @access  Public
exports.getReviews = asyncHandler(async (req, res, next) => {
  let query;

  // Copy req.query
  const reqQuery = { ...req.query };

  // Fields to exclude
  const removeFields = ['select', 'sort', 'page', 'limit'];

  // Loop over removeFields and delete them from reqQuery
  removeFields.forEach(param => delete reqQuery[param]);

  // Create query string
  let queryStr = JSON.stringify(reqQuery);

  // Create operators ($gt, $gte, etc)
  queryStr = queryStr.replace(/\b(gt|gte|lt|lte|in)\b/g, match => `$${match}`);

  // Finding resource
  query = Review.find(JSON.parse(queryStr)).populate({
    path: 'product',
    select: 'name image'
  }).populate({
    path: 'user',
    select: 'name'
  });

  // Select Fields
  if (req.query.select) {
    const fields = req.query.select.split(',').join(' ');
    query = query.select(fields);
  }

  // Sort
  if (req.query.sort) {
    const sortBy = req.query.sort.split(',').join(' ');
    query = query.sort(sortBy);
  } else {
    query = query.sort('-createdAt');
  }

  // Pagination
  const page = parseInt(req.query.page, 10) || 1;
  const limit = parseInt(req.query.limit, 10) || 25;
  const startIndex = (page - 1) * limit;
  const endIndex = page * limit;
  const total = await Review.countDocuments(JSON.parse(queryStr));

  query = query.skip(startIndex).limit(limit);

  // Executing query
  const reviews = await query;

  // Pagination result
  const pagination = {};

  if (endIndex < total) {
    pagination.next = {
      page: page + 1,
      limit
    };
  }

  if (startIndex > 0) {
    pagination.prev = {
      page: page - 1,
      limit
    };
  }

  res.status(200).json({
    success: true,
    count: reviews.length,
    pagination,
    data: reviews
  });
});

// @desc    Get reviews for a product
// @route   GET /api/products/:productId/reviews
// @access  Public
exports.getProductReviews = asyncHandler(async (req, res, next) => {
  const { productId } = req.params;

  console.log('🔍 Getting reviews for product:', productId);

  try {
    // Validate product exists
    const product = await Product.findById(productId);
    if (!product) {
      return next(new ErrorResponse(`Product not found with id of ${productId}`, 404));
    }

    // Get reviews with populated user data
    const reviews = await Review.find({ product: productId })
      .populate({
        path: 'user',
        select: 'firstName lastName name'
      })
      .sort({ createdAt: -1 }); // Most recent first

    console.log(`✅ Found ${reviews.length} reviews for product ${productId}`);

    // Also get the current product rating stats
    const productStats = {
      rating: product.rating || 0,
      totalReviews: product.reviews || 0
    };

    res.status(200).json({
      success: true,
      count: reviews.length,
      data: reviews,
      productStats
    });

  } catch (error) {
    console.error('❌ Error fetching product reviews:', error);
    return next(new ErrorResponse('Failed to fetch reviews', 500));
  }
});

// @desc    Get single review
// @route   GET /api/reviews/:id
// @access  Public
exports.getReview = asyncHandler(async (req, res, next) => {
  const review = await Review.findById(req.params.id)
    .populate({
      path: 'product',
      select: 'name image'
    })
    .populate({
      path: 'user',
      select: 'name'
    });

  if (!review) {
    return next(
      new ErrorResponse(`Review not found with id of ${req.params.id}`, 404)
    );
  }

  res.status(200).json({
    success: true,
    data: review
  });
});

// @desc    Add review
// @route   POST /api/products/:productId/reviews
// @access  Private
exports.addReview = asyncHandler(async (req, res, next) => {
  const { productId } = req.params;
  const userId = req.user._id;
  const { rating, title, comment } = req.body;

  console.log('🔄 Adding review:', {
    productId,
    userId,
    rating,
    title: title?.substring(0, 50) + '...'
  });

  // Validate input
  if (!rating || rating < 1 || rating > 5) {
    return next(new ErrorResponse('Rating must be between 1 and 5', 400));
  }

  if (!title || title.trim().length === 0) {
    return next(new ErrorResponse('Review title is required', 400));
  }

  if (!comment || comment.trim().length === 0) {
    return next(new ErrorResponse('Review comment is required', 400));
  }

  // Check if product exists
  const product = await Product.findById(productId);
  if (!product) {
    return next(new ErrorResponse(`Product not found with id of ${productId}`, 404));
  }

  // Check if user already reviewed this product
  const existingReview = await Review.findOne({
    product: productId,
    user: userId
  });

  if (existingReview) {
    return next(new ErrorResponse('You have already reviewed this product', 400));
  }

  try {
    // Create the review
    const reviewData = {
      product: productId,
      user: userId,
      rating: parseInt(rating),
      title: title.trim(),
      comment: comment.trim()
    };

    const review = await Review.create(reviewData);
    console.log('✅ Review created successfully:', review._id);

    // Add review to product's reviewsRef array
    await Product.findByIdAndUpdate(
      productId,
      {
        $push: { reviewsRef: review._id },
        updatedAt: new Date()
      },
      { new: true }
    );

    // Force immediate rating calculation
    console.log('🔄 Manually triggering rating calculation...');
    const updatedProduct = await Review.calculateAverageRating(productId);

    if (!updatedProduct) {
      console.error('❌ Failed to update product rating');
      return next(new ErrorResponse('Failed to update product rating', 500));
    }

    // Get the updated product data
    const finalProduct = await Product.findById(productId);

    // Populate the user data before returning
    const populatedReview = await Review.findById(review._id)
      .populate({
        path: 'user',
        select: 'firstName lastName name'
      });

    console.log('✅ Review process completed successfully');

    res.status(201).json({
      success: true,
      data: populatedReview,
      productRating: {
        rating: finalProduct.rating,
        reviews: finalProduct.reviews
      }
    });

  } catch (error) {
    console.error('❌ Error creating review:', error);
    return next(new ErrorResponse('Failed to create review', 500));
  }
});

// @desc    Update review
// @route   PUT /api/reviews/:id
// @access  Private
exports.updateReview = asyncHandler(async (req, res, next) => {
  let review = await Review.findById(req.params.id);

  if (!review) {
    return next(
      new ErrorResponse(`Review not found with id of ${req.params.id}`, 404)
    );
  }

  // Make sure review belongs to user or user is admin
  if (review.user.toString() !== req.user.id && req.user.role !== 'admin') {
    return next(
      new ErrorResponse('Not authorized to update this review', 401)
    );
  }

  review = await Review.findByIdAndUpdate(req.params.id, req.body, {
    new: true,
    runValidators: true
  });

  // Recalculate average rating
  await Review.calculateAverageRating(review.product);

  res.status(200).json({
    success: true,
    data: review
  });
});

// @desc    Delete review
// @route   DELETE /api/reviews/:id
// @access  Private
exports.deleteReview = asyncHandler(async (req, res, next) => {
  const review = await Review.findById(req.params.id);

  if (!review) {
    return next(
      new ErrorResponse(`Review not found with id of ${req.params.id}`, 404)
    );
  }

  // Make sure review belongs to user or user is admin
  if (review.user.toString() !== req.user.id && req.user.role !== 'admin') {
    return next(
      new ErrorResponse('Not authorized to delete this review', 401)
    );
  }

  const productId = review.product;

  await review.remove();

  // Remove review from product's reviewsRef array
  await Product.findByIdAndUpdate(
    productId,
    { $pull: { reviewsRef: review._id } },
    { new: true }
  );

  // Recalculate average rating and review count
  await Review.calculateAverageRating(productId);

  res.status(200).json({
    success: true,
    data: {}
  });
});

// @desc    Recalculate ratings for all products (utility endpoint)
// @route   POST /api/reviews/recalculate-all
// @access  Public (for now, to fix the data)
exports.recalculateAllRatings = asyncHandler(async (req, res, next) => {
  try {
    console.log('🔄 Starting recalculation of all product ratings...');

    // Get all products
    const products = await Product.find({});
    console.log(`📊 Found ${products.length} products to recalculate`);

    let updatedCount = 0;
    const results = [];

    for (const product of products) {
      try {
        console.log(`🔄 Processing product: ${product.name} (${product._id})`);

        // Get all reviews for this product
        const reviews = await Review.find({ product: product._id });
        console.log(`📊 Found ${reviews.length} reviews for ${product.name}`);

        let newRating = 0;
        let newReviewCount = reviews.length;

        if (reviews.length > 0) {
          const totalRating = reviews.reduce((sum, review) => sum + review.rating, 0);
          newRating = Math.round((totalRating / reviews.length) * 10) / 10;
        }

        // Update the product
        await Product.findByIdAndUpdate(product._id, {
          rating: newRating,
          reviews: newReviewCount,
          updatedAt: new Date()
        });

        results.push({
          productId: product._id,
          name: product.name,
          oldRating: product.rating,
          newRating: newRating,
          oldReviews: product.reviews,
          newReviews: newReviewCount
        });

        updatedCount++;
        console.log(`✅ Updated ${product.name}: ${product.rating} → ${newRating} (${product.reviews} → ${newReviewCount} reviews)`);

      } catch (productError) {
        console.error(`❌ Error processing product ${product.name}:`, productError);
        results.push({
          productId: product._id,
          name: product.name,
          error: productError.message
        });
      }
    }

    console.log(`✅ Recalculation complete! Updated ${updatedCount}/${products.length} products`);

    res.status(200).json({
      success: true,
      message: `Recalculated ratings for ${updatedCount}/${products.length} products`,
      results: results
    });
  } catch (error) {
    console.error('❌ Error recalculating all ratings:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});