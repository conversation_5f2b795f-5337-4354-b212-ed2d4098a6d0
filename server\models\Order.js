const mongoose = require('mongoose');

const OrderSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  items: [
    {
      product: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Product',
        required: true
      },
      name: {
        type: String,
        required: true
      },
      image: {
        type: String,
        required: true
      },
      price: {
        type: Number,
        required: true
      },
      quantity: {
        type: Number,
        required: true,
        min: 1
      }
    }
  ],
  shippingAddress: {
    firstName: { type: String, required: true },
    lastName: { type: String, required: true },
    address: { type: String, required: true },
    city: { type: String, required: true },
    state: { type: String, required: true },
    zipCode: { type: String, required: true },
    country: { type: String, required: true },
    phone: { type: String }
  },
  paymentInfo: {
    paymentIntentId: { type: String },
    paymentMethod: { type: String },
    paymentStatus: { 
      type: String, 
      enum: ['pending', 'succeeded', 'failed'],
      default: 'pending'
    },
    receiptUrl: { type: String }
  },
  subtotal: {
    type: Number,
    required: true
  },
  tax: {
    type: Number,
    required: true
  },
  shipping: {
    type: Number,
    required: true,
    default: 0
  },
  total: {
    type: Number,
    required: true
  },
  status: {
    type: String,
    enum: ['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded', 'partially_refunded'],
    default: 'pending'
  },
  // Enhanced order management
  orderNumber: {
    type: String
  },
  // Discount information
  discountCode: String,
  discountAmount: {
    type: Number,
    default: 0,
    min: 0
  },
  discountType: {
    type: String,
    enum: ['percentage', 'fixed_amount', 'free_shipping'],
    default: 'fixed_amount'
  },
  // Tax breakdown
  taxBreakdown: [{
    name: String,
    rate: Number,
    amount: Number,
    type: {
      type: String,
      enum: ['percentage', 'fixed_amount'],
      default: 'percentage'
    }
  }],
  taxExempt: {
    type: Boolean,
    default: false
  },
  // Shipping details
  shippingMethod: String,
  trackingNumber: String,
  estimatedDelivery: Date,
  actualDelivery: Date,
  // Refund information
  refundRequests: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Refund'
  }],
  totalRefunded: {
    type: Number,
    default: 0,
    min: 0
  },
  // Order notes and communication
  customerNotes: String,
  adminNotes: String,
  internalNotes: String,
  // Fulfillment
  fulfillmentStatus: {
    type: String,
    enum: ['unfulfilled', 'partial', 'fulfilled'],
    default: 'unfulfilled'
  },
  // Risk assessment
  riskLevel: {
    type: String,
    enum: ['low', 'medium', 'high'],
    default: 'low'
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Update the updatedAt field on save
OrderSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

module.exports = mongoose.model('Order', OrderSchema);