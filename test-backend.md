# Backend Testing Guide

## Step 1: Deploy the Backend
```bash
cd server
vercel --prod
```

## Step 2: Test Endpoints in Order

### 1. Test Basic Server Response
Visit: `https://jaisalgoonlinebackend.vercel.app/`
- Should show: API info with endpoints list

### 2. Test Simple Endpoint (No Database)
Visit: `https://jaisalgoonlinebackend.vercel.app/api/test`
- Should show: "Server is working!" message

### 3. Test Health Check
Visit: `https://jaisalgoonlinebackend.vercel.app/api/health`
- Should show environment variables status
- All should be `true` if configured correctly

### 4. Test Database Connection
Visit: `https://jaisalgoonlinebackend.vercel.app/api/products`
- If this fails, it's a database connection issue

## Step 3: Fix Issues Based on Results

### If `/api/test` fails:
- Server deployment issue
- Check Vercel function logs

### If `/api/health` shows false values:
- Environment variables not set in Vercel
- Go to Vercel Dashboard → Settings → Environment Variables

### If `/api/products` fails:
- MongoDB connection issue
- Check MongoDB Atlas Network Access settings
- Allow access from anywhere (0.0.0.0/0)

## Environment Variables to Set in Vercel Dashboard

Go to your Vercel project → Settings → Environment Variables:

```
VITE_STRIPE_SECRET_KEY = sk_test_51QyG87FsgADt7MeaXmHQyvQYGcjE71yuRGZv0xx0Le8WlZPY9O6rSTnrQOmURCeHc9udXjpF2r28voLB52z3Gv6700T4vpEd6w

MONGODB_URI = mongodb://sulta4567:<EMAIL>:27017,cluster0-shard-00-01.c9q8b.mongodb.net:27017,cluster0-shard-00-02.c9q8b.mongodb.net:27017/jaisalgoonline?replicaSet=atlas-v49dz3-shard-0&ssl=true&authSource=admin

JWT_SECRET = jaisal_go_online_jwt_secret_key_2024_production_secure

BLOB_READ_WRITE_TOKEN = a94wAAm68ve0dsabgkUF00QL
```

Make sure to set these for **Production** environment.

## MongoDB Atlas Setup

1. Go to https://cloud.mongodb.com/
2. Select your project and cluster
3. Go to "Network Access" in left sidebar
4. Click "Add IP Address"
5. Click "Allow Access from Anywhere"
6. Confirm with 0.0.0.0/0

This allows Vercel's dynamic IPs to connect to your database.
