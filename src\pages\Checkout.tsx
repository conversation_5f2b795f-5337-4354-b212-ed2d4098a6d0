import { useState, useEffect, FormEvent } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { ArrowLeft, CreditCard, Truck, Shield, Check, ExternalLink } from 'lucide-react';
import { useCart } from '../context/CartContext';
import { useToast } from '../context/ToastContext';
import { useAuth } from '../context/AuthContext';
import { StripePaymentForm } from '../components/StripePaymentForm';
import { createPaymentIntent } from '../context/StripeContext';
import { verifyPaymentIntent, getPaymentReceiptUrl, storePaymentRecord, PaymentVerificationStatus } from '../utils/stripePaymentVerification';
import { API_URL } from '../utils/env';

// Define checkout steps
const CHECKOUT_STEPS = [
  { id: 1, name: 'Shipping', icon: Truck },
  { id: 2, name: 'Payment', icon: CreditCard },
  { id: 3, name: 'Review', icon: Shield },
  { id: 4, name: 'Complete', icon: Check }
];

export function Checkout() {
  const { items, totalPrice, clearCart } = useCart();
  const { showToast } = useToast();
  const { user, token } = useAuth();
  const navigate = useNavigate();
  
  // State management
  const [currentStep, setCurrentStep] = useState(1);
  const [paymentError, setPaymentError] = useState('');
  const [clientSecret, setClientSecret] = useState('');
  const [paymentIntentId, setPaymentIntentId] = useState('');
  const [isProcessingPayment, setIsProcessingPayment] = useState(false);
  const [receiptUrl, setReceiptUrl] = useState<string | null>(null);

  // Discount code state
  const [discountCode, setDiscountCode] = useState('');
  const [appliedDiscount, setAppliedDiscount] = useState<any>(null);
  const [discountError, setDiscountError] = useState('');
  const [isApplyingDiscount, setIsApplyingDiscount] = useState(false);
  const [formData, setFormData] = useState({
    // Shipping Information
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    state: '',
    zipCode: '',
    country: 'US',
    
    // Payment Information
    cardNumber: '',
    expiryDate: '',
    cvv: '',
    cardName: '',
    
    // Billing same as shipping
    billingDifferent: false
  });

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }));
  };

  // Calculate totals with discount
  const subtotal = totalPrice;
  const discountAmount = appliedDiscount?.discountAmount || 0;
  const finalTotal = Math.max(0, subtotal - discountAmount);

  // Fetch payment intent when reaching payment step
  useEffect(() => {
    if (currentStep === 2 && finalTotal > 0) {
      const fetchPaymentIntent = async () => {
        try {
          setIsProcessingPayment(true);
          // Calculate total with tax and ensure it's an integer in cents
          const totalWithTax = finalTotal * 1.08;
          const amountInCents = Math.round(totalWithTax * 100);
          const { clientSecret: secret, paymentIntentId: id } = await createPaymentIntent(amountInCents);
          setClientSecret(secret);
          setPaymentIntentId(id);
          setIsProcessingPayment(false);
        } catch (error) {
          console.error('Error creating payment intent:', error);
          setPaymentError('Failed to initialize payment. Please try again.');
          setIsProcessingPayment(false);
        }
      };

      fetchPaymentIntent();
    }
  }, [currentStep, finalTotal]);
  
  // Redirect if cart is empty or user not authenticated
  useEffect(() => {
    // Only redirect if we're sure about the state (avoid redirecting during loading)
    if (items.length === 0 && currentStep === 1) {
      navigate('/cart');
      return;
    }
    if (!user && currentStep === 1) {
      navigate('/login');
      return;
    }
  }, [items.length, user, navigate, currentStep]);

  // Clear cart only when the user reaches the confirmation step
  useEffect(() => {
    if (currentStep === 4) {
      clearCart();
    }
  }, [currentStep, clearCart]);

  // Apply discount code function
  const applyDiscountCode = async () => {
    if (!discountCode.trim()) {
      setDiscountError('Please enter a discount code');
      return;
    }

    setIsApplyingDiscount(true);
    setDiscountError('');

    try {
      const response = await fetch(`${API_URL}/api/discounts/validate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          code: discountCode.toUpperCase(),
          orderAmount: totalPrice,
          items: items.map(item => ({
            product: item.product.id,
            category: item.product.category,
            price: item.product.price,
            quantity: item.quantity
          }))
        })
      });

      const data = await response.json();

      if (response.ok && data.success) {
        setAppliedDiscount(data.discount);
        setDiscountError('');
        showToast(`Discount applied! You saved $${data.discountAmount.toFixed(2)}`, 'success');
      } else {
        setDiscountError(data.message || 'Invalid discount code');
        setAppliedDiscount(null);
      }
    } catch (error) {
      console.error('Error applying discount:', error);
      setDiscountError('Failed to apply discount code. Please try again.');
      setAppliedDiscount(null);
    } finally {
      setIsApplyingDiscount(false);
    }
  };

  // Remove applied discount
  const removeDiscount = () => {
    setAppliedDiscount(null);
    setDiscountCode('');
    setDiscountError('');
    showToast('Discount removed', 'info');
  };



  // Create order in database
  const createOrder = async (paymentRecord: any) => {
    try {
      if (!user || !token) {
        throw new Error('User not authenticated');
      }

      const orderData = {
        items: items.map(item => ({
          product: item.id,
          name: item.product.name,
          price: item.product.price,
          image: item.product.image,
          quantity: item.quantity
        })),
        shippingAddress: {
          firstName: formData.firstName,
          lastName: formData.lastName,
          address: formData.address,
          city: formData.city,
          state: formData.state,
          zipCode: formData.zipCode,
          country: formData.country,
          phone: formData.phone
        },
        paymentInfo: {
          paymentIntentId: paymentRecord.paymentIntentId,
          paymentStatus: paymentRecord.status,
          amount: paymentRecord.amount
        },
        subtotal: subtotal,
        discountCode: appliedDiscount?.code || null,
        discountAmount: discountAmount,
        tax: finalTotal * 0.08,
        shipping: 0,
        total: finalTotal * 1.08
      };

      console.log('Creating order with data:', orderData);

      const response = await fetch(`${API_URL}/api/orders`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(orderData)
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Order creation failed:', response.status, errorText);
        let errorData;
        try {
          errorData = JSON.parse(errorText);
        } catch (e) {
          errorData = { error: errorText };
        }
        throw new Error(errorData.error || `Failed to create order (${response.status})`);
      }

      const order = await response.json();
      console.log('Order created successfully:', order);
      return order;
    } catch (error) {
      console.error('Error creating order:', error);
      showToast('Order created but there was an issue saving it. Please contact support.', 'warning');
    }
  };

  // Handle form submission
  const handleSubmit = (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (currentStep < 2) {
      setCurrentStep(currentStep + 1);
    } else if (currentStep === 3) {
      // Process order - this is handled by the Stripe payment form
      console.log('Order submitted:', formData);
      // Only clear cart when moving to the final confirmation step
      setCurrentStep(4); // Order confirmation
    }
  };
  
  // Handle successful payment
  const handlePaymentSuccess = async () => {
    try {
      // Verify the payment intent
      const paymentRecord = await verifyPaymentIntent(paymentIntentId);

      // Store the payment record
      storePaymentRecord(paymentRecord);

      // Get the receipt URL
      const url = await getPaymentReceiptUrl(paymentIntentId);
      setReceiptUrl(url);

      // Create order in database
      await createOrder(paymentRecord);

      // Check if payment was successful
      if (paymentRecord.status === PaymentVerificationStatus.SUCCEEDED) {
        setCurrentStep(4); // Move directly to confirmation step after successful payment
        showToast('Payment processed successfully!', 'success');
      } else {
        setPaymentError('Payment verification failed. Please contact support.');
        showToast('Payment verification failed. Please contact support.', 'error');
      }
    } catch (error) {
      console.error('Error verifying payment:', error);
      // Still proceed to confirmation step in test mode
      setCurrentStep(4);
      showToast('Payment processed successfully! (Test Mode)', 'success');
    }
  };
  
  // Handle payment error
  const handlePaymentError = (error: string) => {
    setPaymentError(error);
    showToast(`Payment failed: ${error}`, 'error');
  };

  // Render empty cart message if cart is empty (except on confirmation step)
  if (items.length === 0 && currentStep !== 4) {
    return (
      <div className="min-h-screen bg-gray-50 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">Checkout</h1>
            <p className="text-lg text-gray-600 mb-8">Your cart is empty</p>
            <Link
              to="/shop"
              className="inline-flex items-center space-x-2 bg-gradient-to-r from-green-600 to-green-700 text-white px-8 py-3 rounded-2xl hover:from-green-700 hover:to-green-800 transition-all duration-300 font-semibold shadow-xl hover:shadow-2xl transform hover:scale-105"
            >
              <span>Start Shopping</span>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  // Render checkout process
  return (
    <div className="min-h-screen bg-gray-50 py-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-12">
          <div>
            <h1 className="text-4xl font-bold text-gray-900 mb-4">Secure Checkout</h1>
            <p className="text-lg text-gray-600">Complete your order safely and securely</p>
          </div>
          <Link
            to="/cart"
            className="inline-flex items-center space-x-2 text-green-600 hover:text-green-700 font-semibold transition-colors"
          >
            <ArrowLeft className="h-5 w-5" />
            <span>Back to Cart</span>
          </Link>
        </div>

        {/* Progress Steps */}
        <div className="bg-white rounded-3xl shadow-lg border border-gray-100 p-8 mb-8">
          <nav aria-label="Progress">
            <ol className="flex items-center justify-center space-x-8">
              {CHECKOUT_STEPS.map((step) => (
                <li key={step.id} className="flex items-center">
                  <div className={`flex items-center justify-center w-12 h-12 rounded-full border-2 transition-all duration-300 ${
                    currentStep >= step.id
                      ? 'bg-gradient-to-br from-green-500 to-green-600 border-green-500 text-white shadow-lg'
                      : 'border-gray-300 text-gray-500 bg-white'
                  }`}>
                    <step.icon className="h-6 w-6" />
                  </div>
                  <span className={`ml-3 text-sm font-semibold transition-colors ${
                    currentStep >= step.id ? 'text-green-600' : 'text-gray-500'
                  }`}>
                    {step.name}
                  </span>
                </li>
              ))}
            </ol>
          </nav>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">
            {/* Use different containers based on step to avoid nested forms */}
            {currentStep === 2 ? (
              <div className="space-y-6">
                {/* Step 2: Payment Information */}
                <div>
                  {isProcessingPayment ? (
                    <div className="bg-white rounded-3xl shadow-lg border border-gray-100 p-8 flex justify-center items-center">
                      <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-green-500"></div>
                      <span className="ml-3 text-gray-600">Initializing payment...</span>
                    </div>
                  ) : clientSecret ? (
                    <StripePaymentForm 
                      onSuccess={handlePaymentSuccess}
                      onError={handlePaymentError}
                      clientSecret={clientSecret}
                      formData={formData}
                    />
                  ) : (
                    <div className="bg-white rounded-3xl shadow-lg border border-gray-100 p-8">
                      <p className="text-red-600">{paymentError || 'Unable to initialize payment. Please try again.'}</p>
                      <button
                        type="button"
                        onClick={() => setCurrentStep(1)}
                        className="mt-4 px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
                      >
                        Back to Shipping
                      </button>
                    </div>
                  )}
                </div>
                
                {/* Navigation Buttons for Step 2 */}
                <div className="flex justify-between">
                  <button
                    type="button"
                    onClick={() => setCurrentStep(currentStep - 1)}
                    className="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
                  >
                    Previous
                  </button>
                </div>
              </div>
            ) : (
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Step 1: Shipping Information */}
                {currentStep === 1 && (
                <div className="bg-white rounded-3xl shadow-lg border border-gray-100 p-8">
                  <div className="flex items-center space-x-3 mb-8">
                    <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center">
                      <Truck className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <h2 className="text-2xl font-bold text-gray-900">Shipping Information</h2>
                      <p className="text-sm text-gray-600">Where should we deliver your order?</p>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-semibold text-gray-700 mb-2">
                        First Name *
                      </label>
                      <input
                        type="text"
                        name="firstName"
                        required
                        value={formData.firstName}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-colors"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Last Name *
                      </label>
                      <input
                        type="text"
                        name="lastName"
                        required
                        value={formData.lastName}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Email *
                      </label>
                      <input
                        type="email"
                        name="email"
                        required
                        value={formData.email}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Phone
                      </label>
                      <input
                        type="tel"
                        name="phone"
                        value={formData.phone}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-slate-500 focus:border-transparent"
                      />
                    </div>
                    
                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Address *
                      </label>
                      <input
                        type="text"
                        name="address"
                        required
                        value={formData.address}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-slate-500 focus:border-transparent"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        City *
                      </label>
                      <input
                        type="text"
                        name="city"
                        required
                        value={formData.city}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-slate-500 focus:border-transparent"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        State *
                      </label>
                      <input
                        type="text"
                        name="state"
                        required
                        value={formData.state}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-slate-500 focus:border-transparent"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        ZIP Code *
                      </label>
                      <input
                        type="text"
                        name="zipCode"
                        required
                        value={formData.zipCode}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-slate-500 focus:border-transparent"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Country *
                      </label>
                      <select
                        name="country"
                        required
                        value={formData.country}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-slate-500 focus:border-transparent"
                      >
                        <option value="US">United States</option>
                        <option value="CA">Canada</option>
                        <option value="GB">United Kingdom</option>
                        <option value="AU">Australia</option>
                        <option value="DE">Germany</option>
                        <option value="FR">France</option>
                        <option value="JP">Japan</option>
                        <option value="CN">China</option>
                        <option value="IN">India</option>
                        <option value="BR">Brazil</option>
                      </select>
                    </div>
                  </div>
                </div>
              )}

              {/* Step 3: Review Order */}
              {currentStep === 3 && (
                <div className="bg-white rounded-3xl shadow-lg border border-gray-100 p-8">
                  <div className="flex items-center space-x-3 mb-8">
                    <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center">
                      <Shield className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <h2 className="text-2xl font-bold text-gray-900">Review Your Order</h2>
                      <p className="text-sm text-gray-600">Please review your order details before finalizing</p>
                    </div>
                  </div>
                  
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 mb-2">Shipping Information</h3>
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <p>{formData.firstName} {formData.lastName}</p>
                        <p>{formData.address}</p>
                        <p>{formData.city}, {formData.state} {formData.zipCode}</p>
                        <p>{{
                          'US': 'United States',
                          'CA': 'Canada',
                          'GB': 'United Kingdom',
                          'AU': 'Australia',
                          'DE': 'Germany',
                          'FR': 'France',
                          'JP': 'Japan',
                          'CN': 'China',
                          'IN': 'India',
                          'BR': 'Brazil'
                        }[formData.country] || formData.country}</p>
                        <p>{formData.email}</p>
                        {formData.phone && <p>{formData.phone}</p>}
                      </div>
                    </div>
                    
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 mb-2">Payment</h3>
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <p>Payment method: Credit Card (Stripe)</p>
                        <p>Payment status: Processed</p>
                      </div>
                    </div>
                  </div>
                </div>
              )}
              
              {/* Step 4: Order Confirmation */}
              {currentStep === 4 && (
                <div className="bg-white rounded-3xl shadow-lg border border-gray-100 p-8 text-center">
                  <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <Check className="h-8 w-8 text-green-600" />
                  </div>
                  <h2 className="text-2xl font-bold text-gray-900 mb-2">Order Confirmed!</h2>
                  <p className="text-gray-600 mb-6">Thank you for your purchase. We've received your order and will process it right away.</p>
                  
                  {/* Payment Verification Section */}
                  <div className="bg-gray-50 rounded-xl p-6 mb-6">
                    <h3 className="text-lg font-semibold text-gray-800 mb-3">Payment Verification</h3>
                    <div className="flex items-center justify-center space-x-2 mb-4">
                      <div className="w-4 h-4 bg-green-500 rounded-full"></div>
                      <span className="text-green-600 font-medium">Payment Verified</span>
                    </div>
                    
                    <div className="text-sm text-gray-600 mb-4">
                      <p>Payment ID: <span className="font-mono text-gray-800">{paymentIntentId}</span></p>
                      <p>Amount: <span className="font-medium">${(totalPrice * 1.08).toFixed(2)}</span></p>
                      <p>Date: <span>{new Date().toLocaleString()}</span></p>
                    </div>
                    
                    {receiptUrl && (
                      <a 
                        href={receiptUrl} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors"
                      >
                        View Receipt <ExternalLink className="h-4 w-4 ml-1" />
                      </a>
                    )}
                    
                    <div className="mt-4 text-xs text-gray-500">
                      <p>This was a test payment. In a real application, this payment would be processed by Stripe and funds would be transferred to the merchant account.</p>
                      <p>You can view test payments in the <a href="https://dashboard.stripe.com/test/payments" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">Stripe Dashboard</a>.</p>
                    </div>
                  </div>
                  
                  <p className="text-gray-600 mb-6">Order confirmation has been sent to: <span className="font-semibold">{formData.email}</span></p>
                  
                  <div className="flex flex-col sm:flex-row gap-4">
                    <Link
                      to="/shop"
                      className="inline-flex items-center space-x-2 bg-gradient-to-r from-green-600 to-green-700 text-white px-8 py-3 rounded-2xl hover:from-green-700 hover:to-green-800 transition-all duration-300 font-semibold shadow-xl hover:shadow-2xl transform hover:scale-105"
                    >
                      <span>Continue Shopping</span>
                    </Link>
                    <Link
                      to="/account"
                      className="inline-flex items-center space-x-2 bg-gradient-to-r from-blue-600 to-blue-700 text-white px-8 py-3 rounded-2xl hover:from-blue-700 hover:to-blue-800 transition-all duration-300 font-semibold shadow-xl hover:shadow-2xl transform hover:scale-105"
                    >
                      <span>View Payment History</span>
                    </Link>
                  </div>
                </div>
              )}
              
              {/* Navigation Buttons */}
              <div className="flex justify-between">
                {currentStep > 1 && currentStep < 3 && (
                  <button
                    type="button"
                    onClick={() => setCurrentStep(currentStep - 1)}
                    className="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
                  >
                    Previous
                  </button>
                )}
                
                {currentStep === 1 && (
                  <button
                    type="submit"
                    className="ml-auto px-6 py-2 bg-gradient-to-r from-green-600 to-green-700 text-white rounded-xl hover:from-green-700 hover:to-green-800 transition-all duration-300 font-semibold shadow-lg hover:shadow-xl"
                  >
                    Continue to Payment
                  </button>
                )}
                
                {currentStep === 3 && (
                  <button
                    type="button"
                    onClick={() => setCurrentStep(4)}
                    className="ml-auto px-6 py-2 bg-gradient-to-r from-green-600 to-green-700 text-white rounded-xl hover:from-green-700 hover:to-green-800 transition-all duration-300 font-semibold shadow-lg hover:shadow-xl"
                  >
                    Place Order
                  </button>
                )}
              </div>
            </form>
            )}
          </div>
          
          {/* Order Summary */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-sm p-6 sticky top-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Order Summary</h3>
              
              <div className="space-y-4 mb-6">
                {items.map((item) => (
                  <div key={`${item.id}-default`} className="flex items-center space-x-3">
                    <img
                      src={item.product.image}
                      alt={item.product.name}
                      className="w-12 h-12 object-cover rounded-md"
                    />
                    <div className="flex-1">
                      <h4 className="text-sm font-medium text-gray-900">{item.product.name}</h4>
                      <p className="text-sm text-gray-500">Qty: {item.quantity}</p>
                    </div>
                    <span className="text-sm font-medium text-gray-900">
                      ${(item.product.price * item.quantity).toFixed(2)}
                    </span>
                  </div>
                ))}
              </div>
              
              {/* Discount Code Section */}
              <div className="border-t border-gray-200 pt-4 mb-4">
                <h4 className="text-sm font-medium text-gray-900 mb-3">Discount Code</h4>

                {!appliedDiscount ? (
                  <div className="space-y-2">
                    <div className="flex space-x-2">
                      <input
                        type="text"
                        placeholder="Enter discount code"
                        value={discountCode}
                        onChange={(e) => setDiscountCode(e.target.value.toUpperCase())}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 text-sm"
                        disabled={isApplyingDiscount}
                      />
                      <button
                        onClick={applyDiscountCode}
                        disabled={isApplyingDiscount || !discountCode.trim()}
                        className="px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {isApplyingDiscount ? 'Applying...' : 'Apply'}
                      </button>
                    </div>
                    {discountError && (
                      <p className="text-red-600 text-xs">{discountError}</p>
                    )}
                  </div>
                ) : (
                  <div className="bg-green-50 border border-green-200 rounded-md p-3">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-green-800">
                          {appliedDiscount.name} ({appliedDiscount.code})
                        </p>
                        <p className="text-xs text-green-600">
                          {appliedDiscount.description}
                        </p>
                      </div>
                      <button
                        onClick={removeDiscount}
                        className="text-green-600 hover:text-green-800 text-sm font-medium"
                      >
                        Remove
                      </button>
                    </div>
                  </div>
                )}
              </div>

              <div className="border-t border-gray-200 pt-4 space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Subtotal</span>
                  <span className="font-medium">${subtotal.toFixed(2)}</span>
                </div>

                {appliedDiscount && (
                  <div className="flex justify-between text-sm text-green-600">
                    <span>Discount ({appliedDiscount.code})</span>
                    <span className="font-medium">-${discountAmount.toFixed(2)}</span>
                  </div>
                )}

                <div className="flex justify-between text-sm">
                  <span>Tax (8%)</span>
                  <span className="font-medium">${(finalTotal * 0.08).toFixed(2)}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Shipping</span>
                  <span className="font-medium">Free</span>
                </div>
                <div className="flex justify-between text-base font-semibold mt-4 pt-4 border-t border-gray-200">
                  <span>Total</span>
                  <span>${(finalTotal * 1.08).toFixed(2)}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
