/**
 * Stripe Payment Verification Utility
 * 
 * This utility provides functions to verify and track Stripe payments,
 * including test mode payments.
 */

import { API_URL, IS_DEVELOPMENT } from './env';

/**
 * Payment verification status
 */
export enum PaymentVerificationStatus {
  PENDING = 'pending',
  SUCCEEDED = 'succeeded',
  FAILED = 'failed',
  REQUIRES_ACTION = 'requires_action'
}

/**
 * Payment record interface
 */
export interface PaymentRecord {
  paymentIntentId: string;
  amount: number;
  status: PaymentVerificationStatus;
  createdAt: Date;
  metadata?: Record<string, any>;
}

/**
 * Verify a payment intent with the Stripe API
 * @param paymentIntentId The payment intent ID to verify
 * @returns The payment verification result
 */
export async function verifyPaymentIntent(paymentIntentId: string): Promise<PaymentRecord> {
  try {
    // Call the server API to verify the payment intent
    const response = await fetch(`${API_URL}/api/payment/verify-payment-intent/${paymentIntentId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to verify payment intent');
    }

    return await response.json();
  } catch (error) {
    console.error('Error verifying payment intent:', error);
    
    // Fallback for development/testing
    if (IS_DEVELOPMENT) {
      console.warn('Using mock payment verification as fallback');
      return {
        paymentIntentId,
        amount: 0, // This would be populated with actual amount in real implementation
        status: PaymentVerificationStatus.SUCCEEDED,
        createdAt: new Date(),
        metadata: {
          testMode: true,
          verificationSource: 'client-side-fallback'
        }
      };
    }
    
    throw error;
  }
}

/**
 * Get payment receipt URL
 * @param paymentIntentId The payment intent ID
 * @returns The receipt URL or null if not available
 */
export async function getPaymentReceiptUrl(paymentIntentId: string): Promise<string | null> {
  try {
    // Call the server API to get the payment receipt URL
    const response = await fetch(`${API_URL}/api/payment/payment-receipt/${paymentIntentId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to get payment receipt');
    }

    const data = await response.json();
    return data.receiptUrl;
  } catch (error) {
    console.error('Error getting payment receipt:', error);
    
    // Fallback for development/testing
    if (IS_DEVELOPMENT) {
      console.warn('Using mock payment receipt URL as fallback');
      return `https://dashboard.stripe.com/test/payments/${paymentIntentId}`;
    }
    
    return null;
  }
}

/**
 * Store payment record in local storage for client-side tracking
 * @param payment The payment record to store
 */
export function storePaymentRecord(payment: PaymentRecord): void {
  try {
    // Get existing payment records
    const existingRecordsJson = localStorage.getItem('paymentRecords');
    const existingRecords: PaymentRecord[] = existingRecordsJson 
      ? JSON.parse(existingRecordsJson) 
      : [];
    
    // Add new payment record
    existingRecords.push({
      ...payment,
      createdAt: new Date() // Ensure date is current
    });
    
    // Store updated records
    localStorage.setItem('paymentRecords', JSON.stringify(existingRecords));
  } catch (error) {
    console.error('Error storing payment record:', error);
  }
}

/**
 * Get all stored payment records
 * @returns Array of payment records
 */
export function getPaymentRecords(): PaymentRecord[] {
  try {
    const recordsJson = localStorage.getItem('paymentRecords');
    return recordsJson ? JSON.parse(recordsJson) : [];
  } catch (error) {
    console.error('Error retrieving payment records:', error);
    return [];
  }
}