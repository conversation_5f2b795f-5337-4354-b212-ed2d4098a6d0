import { useState, useEffect } from 'react';
import { Review } from '../types';
import ReviewForm from './ReviewForm';
import { API_URL } from '../utils/env';
import { Star } from 'lucide-react';
import { useAuth } from '../context/AuthContext';
import { productUpdateManager } from '../utils/productUpdateManager';

interface ReviewSectionProps {
  productId: string;
}

export default function ReviewSection({ productId }: ReviewSectionProps) {
  const [reviews, setReviews] = useState<Review[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [showReviewForm, setShowReviewForm] = useState<boolean>(false);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [userHasReviewed, setUserHasReviewed] = useState<boolean>(false);
  // Add loading indicator to ReviewForm component
  useEffect(() => {
    if (showReviewForm) {
      const reviewForm = document.querySelector('.review-form');
      if (reviewForm) {
        reviewForm.classList.toggle('submitting', isSubmitting);
      }
    }
  }, [isSubmitting, showReviewForm]);
  const [averageRating, setAverageRating] = useState<number>(0);
  
  // Use the auth context to get user information
  const { user, token } = useAuth();

  // Fetch reviews for the product from the database
  useEffect(() => {
    const fetchReviews = async () => {
      try {
        setIsLoading(true);
        setError(null); // Clear any previous errors
        
        // Make sure we're fetching reviews for the current product ID from the database
        // Add cache busting parameter
        const response = await fetch(`${API_URL}/api/products/${productId}/reviews?t=${Date.now()}`);
        
        if (!response.ok) {
          throw new Error('Failed to fetch reviews from database');
        }
        
        const data = await response.json();

        console.log('Reviews API response:', data);
        console.log('Reviews data type:', typeof data);
        console.log('Reviews data.data type:', typeof data.data);
        console.log('Is data.data an array?', Array.isArray(data.data));

        // Handle different response formats
        let reviewsArray = [];
        if (Array.isArray(data)) {
          // Direct array response
          reviewsArray = data;
        } else if (Array.isArray(data.data)) {
          // Standard API response with data property
          reviewsArray = data.data;
        } else if (data.reviews && Array.isArray(data.reviews)) {
          // Alternative format with reviews property
          reviewsArray = data.reviews;
        } else {
          console.warn('Unexpected API response format:', data);
          reviewsArray = [];
        }

        // Verify that all reviews belong to this product
        const filteredReviews = reviewsArray.filter((review: any) =>
          review.product === productId || review.product._id === productId
        );
        
        // Map the reviews to include userName from the populated user object
        const reviewsWithUserNames = filteredReviews.map((review: any) => {
          let userName = 'Anonymous';

          if (review.user) {
            // Try multiple approaches to get the user name
            if (review.user.name && review.user.name !== 'undefined undefined') {
              userName = review.user.name;
            } else if (review.user.firstName && review.user.lastName) {
              userName = `${review.user.firstName} ${review.user.lastName}`;
            } else if (review.user.firstName) {
              userName = review.user.firstName;
            } else if (review.user.lastName) {
              userName = review.user.lastName;
            } else if (review.user.email) {
              // Fallback to email if no name fields are available
              userName = review.user.email.split('@')[0];
            } else if (user && (review.user._id === user.id || review.user === user.id)) {
              // If this is the current user's review and we have user context, use that
              userName = user.firstName && user.lastName ? `${user.firstName} ${user.lastName}` : 'You';
            }
          }

          return {
            ...review,
            userName
          };
        });

        // Use only real reviews from database (no dummy reviews)
        const finalReviews = reviewsWithUserNames;

        setReviews(finalReviews);

        // Check if current user has already reviewed this product
        if (user) {
          const userReview = reviewsArray.find((review: any) =>
            review.user?._id === user.id || review.user === user.id
          );
          setUserHasReviewed(!!userReview);
        } else {
          setUserHasReviewed(false);
        }

        // Use the product stats from the server response if available
        if (data.productStats) {
          console.log('📊 Using server product stats for rating display:', data.productStats);
          setAverageRating(data.productStats.rating || 0);
        } else {
          // Fallback: Calculate from actual reviews
          if (finalReviews.length > 0) {
            const total = finalReviews.reduce((sum: number, review: any) => sum + review.rating, 0);
            const calculatedRating = total / finalReviews.length;
            setAverageRating(calculatedRating);
            console.log('📊 Calculated rating from reviews:', calculatedRating);
          } else {
            setAverageRating(0);
          }
        }
      } catch (err) {
        setError('Failed to load reviews from database. Please try again later.');
        console.error('Error fetching reviews from database:', err);
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchReviews();
  }, [productId, user]); // Re-fetch when productId or user changes

  const handleSubmitReview = async (reviewData: {
    rating: number;
    title: string;
    comment: string;
  }) => {
    try {
      setIsSubmitting(true);
      
      if (!token) {
        setError('You must be logged in to submit a review');
        return;
      }
      
      // Submit review to the database via API
      const response = await fetch(`${API_URL}/api/products/${productId}/reviews`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(reviewData),
      });
      
      // Parse the response JSON (only once)
      const data = await response.json();

      if (!response.ok) {
        // Handle specific error for duplicate reviews
        if (response.status === 400 && data.error?.includes('already reviewed')) {
          setError('You have already reviewed this product. Each user can only submit one review per product.');
          setUserHasReviewed(true);
          setShowReviewForm(false);
          return;
        }
        throw new Error(data.error || 'Failed to save review to database');
      }
      
      // Add the new review to the list with proper user name
      let userName = 'You';
      
      // Get the user's name from the auth context if available
      if (user && user.firstName && user.lastName) {
        userName = `${user.firstName} ${user.lastName}`;
      }
      
      // Create the new review object from database data
      const newReview = {
        ...data.data,
        userName: userName,
      };
      
      // Update reviews state with the new review from database
      const updatedReviews = [...reviews, newReview];
      setReviews(updatedReviews);

      // Use the rating data from the server response if available
      const serverRating = response.data.productRating;
      let newAverageRating, newReviewCount;

      if (serverRating) {
        newAverageRating = serverRating.rating;
        newReviewCount = serverRating.reviews;
      } else {
        // Fallback to client-side calculation
        const newTotal = updatedReviews.reduce((sum, review) => sum + review.rating, 0);
        newAverageRating = newTotal / updatedReviews.length;
        newReviewCount = updatedReviews.length;
      }

      setAverageRating(newAverageRating);

      console.log('✅ Review added successfully, notifying all components:', {
        productId,
        newRating: newAverageRating,
        reviewCount: newReviewCount,
        serverData: !!serverRating
      });

      // Use the new product update manager for better real-time updates
      productUpdateManager.notifyUpdate({
        productId,
        rating: newAverageRating,
        reviews: newReviewCount,
        timestamp: Date.now()
      });
      
      // Hide the review form
      setShowReviewForm(false);
      setUserHasReviewed(true);

      // Show a success message
      setError(null); // Clear any previous errors
    } catch (err: any) {
      console.error('❌ Error submitting review:', err);

      // Handle different types of errors
      let errorMessage = 'Failed to submit review. Please try again.';

      if (err.response?.data?.error) {
        errorMessage = err.response.data.error;
      } else if (err.message) {
        errorMessage = err.message;
      }

      setError(errorMessage);

      // Show toast notification for better UX
      if (typeof window !== 'undefined' && window.dispatchEvent) {
        const errorEvent = new CustomEvent('showToast', {
          detail: {
            message: errorMessage,
            type: 'error'
          }
        });
        window.dispatchEvent(errorEvent);
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="mt-8">
      <div className="flex items-center mb-6">
        <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center mr-3">
          <span className="text-white text-sm">⭐</span>
        </div>
        <h2 className="text-2xl font-bold text-gray-900">Customer Reviews</h2>
      </div>

      <div className="flex items-center space-x-6 mb-8 p-6 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-2xl">
        <div className="flex items-center">
          <span className="text-4xl font-bold text-green-700 mr-3">{averageRating.toFixed(1)}</span>
          <div className="flex items-center space-x-1">
            {[...Array(5)].map((_, i) => (
              <Star
                key={i}
                className={`h-6 w-6 ${i < Math.floor(averageRating) ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
              />
            ))}
          </div>
        </div>
        <div className="flex flex-col">
          <span className="text-sm font-semibold text-green-700">
            {averageRating.toFixed(1)} out of 5
          </span>
          <span className="text-xs text-green-600">
            Based on {reviews.length} review{reviews.length !== 1 ? 's' : ''}
          </span>
        </div>
      </div>
      
      {error && <div className="bg-red-50 text-red-600 p-3 rounded-md mb-4">{error}</div>}
      
      {isLoading && <div className="text-center py-4 mb-4">Loading reviews...</div>}
      
      {!user ? (
        <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-xl">
          <p className="text-green-700 text-sm font-medium">
            🔐 Please <span className="font-bold">log in</span> to write a review for this product.
          </p>
        </div>
      ) : userHasReviewed ? (
        <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
          <p className="text-green-700 text-sm">
            ✓ You have already reviewed this product. Thank you for your feedback!
          </p>
        </div>
      ) : !showReviewForm ? (
        <button
          onClick={() => setShowReviewForm(true)}
          className="mb-6 inline-flex items-center px-6 py-3 border border-transparent text-sm font-semibold rounded-xl shadow-lg text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200 transform hover:scale-105"
        >
          ✍️ Write a Review
        </button>
      ) : (
        <div className="mb-8">
          <ReviewForm
            onSubmit={handleSubmitReview}
            onCancel={() => setShowReviewForm(false)}
          />
        </div>
      )}
      
      {/* Display Reviews */}
      {reviews.length > 0 && (
        <div className="mt-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-6 flex items-center">
            <span className="w-6 h-6 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center mr-2">
              <span className="text-white text-xs">💬</span>
            </span>
            Recent Reviews ({reviews.length})
          </h3>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {reviews.slice(0, 6).map((review) => (
              <div key={review._id || review.id} className="bg-gradient-to-br from-white to-green-50/30 border border-green-100 p-4 rounded-xl shadow-sm hover:shadow-md transition-all duration-200 hover:scale-105">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-2">
                    <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center">
                      <span className="text-white font-semibold text-xs">
                        {review.userName ? review.userName.charAt(0).toUpperCase() : 'A'}
                      </span>
                    </div>
                    <div>
                      <p className="text-xs font-medium text-gray-600">{review.userName || 'Anonymous'}</p>
                      <div className="flex items-center space-x-1">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className={`h-3 w-3 ${i < review.rating ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
                          />
                        ))}
                      </div>
                    </div>
                  </div>
                  <span className="text-xs text-gray-500 bg-green-100 px-2 py-1 rounded-full">
                    {new Date(review.createdAt).toLocaleDateString()}
                  </span>
                </div>

                <h4 className="font-semibold text-gray-900 text-sm mb-2 line-clamp-2">{review.title}</h4>

                {review.comment && (
                  <p className="text-gray-600 text-xs leading-relaxed line-clamp-3">
                    {review.comment}
                  </p>
                )}
              </div>
            ))}
          </div>

          {reviews.length > 6 && (
            <div className="text-center mt-6">
              <button className="text-green-600 hover:text-green-700 font-medium text-sm bg-green-50 hover:bg-green-100 px-4 py-2 rounded-full transition-colors duration-200">
                View All {reviews.length} Reviews
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
}