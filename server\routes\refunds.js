const express = require('express');
const router = express.Router();
const Refund = require('../models/Refund');
const Order = require('../models/Order');
const { protect, admin } = require('../middleware/auth');

/**
 * @route   POST /api/refunds/request
 * @desc    Create a refund request (Customer)
 * @access  Private
 */
router.post('/request', protect, async (req, res) => {
  try {
    const { orderId, items, reason, customerNotes, images } = req.body;

    console.log('Refund request received:', { orderId, items, reason, userId: req.user._id });

    // Verify order belongs to user
    const order = await Order.findById(orderId).populate('items.product');
    if (!order) {
      return res.status(404).json({ success: false, message: 'Order not found' });
    }

    console.log('Order found:', { orderId: order._id, status: order.status, userId: order.user });

    if (order.user.toString() !== req.user._id.toString()) {
      return res.status(403).json({ success: false, message: 'Not authorized to refund this order' });
    }
    
    // Check if order is eligible for refund
    if (!['pending', 'confirmed', 'processing', 'shipped', 'delivered'].includes(order.status)) {
      return res.status(400).json({ success: false, message: `Order is not eligible for refund. Current status: ${order.status}. Only pending, confirmed, processing, shipped, or delivered orders can be refunded.` });
    }
    
    // Calculate refund amount
    let refundAmount = 0;
    const refundItems = [];
    
    for (const item of items) {
      const orderItem = order.items.find(oi => oi.product._id.toString() === item.productId);
      if (!orderItem) {
        return res.status(400).json({ success: false, message: `Product ${item.productId} not found in order` });
      }
      
      if (item.quantity > orderItem.quantity) {
        return res.status(400).json({ success: false, message: `Cannot refund more than ordered quantity` });
      }
      
      const itemRefundAmount = (orderItem.price * item.quantity);
      refundAmount += itemRefundAmount;
      
      refundItems.push({
        product: item.productId,
        name: orderItem.name,
        quantity: item.quantity,
        price: orderItem.price,
        reason: item.reason || reason
      });
    }
    
    // Create refund request
    const refund = new Refund({
      order: orderId,
      user: req.user._id,
      items: refundItems,
      refundAmount,
      reason,
      customerNotes,
      images: images || [],
      status: 'pending'
    });
    
    await refund.save();
    
    // Add refund to order
    order.refundRequests.push(refund._id);
    await order.save();
    
    res.status(201).json({
      success: true,
      message: 'Refund request submitted successfully',
      refund
    });
  } catch (error) {
    console.error('Error creating refund request:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

/**
 * @route   GET /api/refunds/my-requests
 * @desc    Get user's refund requests
 * @access  Private
 */
router.get('/my-requests', protect, async (req, res) => {
  try {
    const refunds = await Refund.find({ user: req.user._id })
      .populate('order', 'orderNumber createdAt total')
      .populate('items.product', 'name image')
      .sort({ createdAt: -1 });
    
    res.json({ success: true, refunds });
  } catch (error) {
    console.error('Error fetching user refunds:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

/**
 * @route   GET /api/refunds/admin
 * @desc    Get all refund requests (Admin)
 * @access  Private/Admin
 */
router.get('/admin', protect, admin, async (req, res) => {
  try {
    const { status, page = 1, limit = 20 } = req.query;
    
    let filter = {};
    if (status && status !== 'all') {
      filter.status = status;
    }
    
    const refunds = await Refund.find(filter)
      .populate('user', 'firstName lastName email')
      .populate('order', 'orderNumber createdAt total')
      .populate('items.product', 'name image')
      .populate('processedBy', 'firstName lastName')
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);
    
    const total = await Refund.countDocuments(filter);
    
    res.json({
      success: true,
      refunds,
      pagination: {
        page: parseInt(page),
        pages: Math.ceil(total / limit),
        total
      }
    });
  } catch (error) {
    console.error('Error fetching refunds:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

/**
 * @route   PUT /api/refunds/:id/process
 * @desc    Process refund request (Admin)
 * @access  Private/Admin
 */
router.put('/:id/process', protect, admin, async (req, res) => {
  try {
    const { status, adminNotes, refundMethod, refundTransactionId } = req.body;
    
    const refund = await Refund.findById(req.params.id)
      .populate('order')
      .populate('user');
    
    if (!refund) {
      return res.status(404).json({ success: false, message: 'Refund not found' });
    }
    
    refund.status = status;
    refund.adminNotes = adminNotes;
    refund.processedBy = req.user._id;
    refund.processedAt = new Date();
    
    if (refundMethod) refund.refundMethod = refundMethod;
    if (refundTransactionId) refund.refundTransactionId = refundTransactionId;
    
    await refund.save();
    
    // Update order if refund is approved
    if (status === 'approved' || status === 'completed') {
      const order = refund.order;
      order.totalRefunded += refund.refundAmount;
      
      if (order.totalRefunded >= order.total) {
        order.status = 'refunded';
      } else {
        order.status = 'partially_refunded';
      }
      
      await order.save();
    }
    
    res.json({
      success: true,
      message: `Refund ${status} successfully`,
      refund
    });
  } catch (error) {
    console.error('Error processing refund:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

/**
 * @route   GET /api/refunds/stats
 * @desc    Get refund statistics (Admin)
 * @access  Private/Admin
 */
router.get('/stats', protect, admin, async (req, res) => {
  try {
    const totalRefunds = await Refund.countDocuments();
    const pendingRefunds = await Refund.countDocuments({ status: 'pending' });
    const approvedRefunds = await Refund.countDocuments({ status: { $in: ['approved', 'completed'] } });
    
    const totalRefundAmount = await Refund.aggregate([
      { $match: { status: { $in: ['approved', 'completed'] } } },
      { $group: { _id: null, total: { $sum: '$refundAmount' } } }
    ]);
    
    const refundsByMonth = await Refund.aggregate([
      {
        $group: {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' }
          },
          count: { $sum: 1 },
          amount: { $sum: '$refundAmount' }
        }
      },
      { $sort: { '_id.year': -1, '_id.month': -1 } },
      { $limit: 12 }
    ]);
    
    res.json({
      success: true,
      stats: {
        totalRefunds,
        pendingRefunds,
        approvedRefunds,
        totalRefundAmount: totalRefundAmount[0]?.total || 0,
        refundsByMonth
      }
    });
  } catch (error) {
    console.error('Error fetching refund stats:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

/**
 * @route   GET /api/refunds/:id
 * @desc    Get refund details
 * @access  Private
 */
router.get('/:id', protect, async (req, res) => {
  try {
    const refund = await Refund.findById(req.params.id)
      .populate('user', 'firstName lastName email')
      .populate('order', 'orderNumber createdAt total status')
      .populate('items.product', 'name image price')
      .populate('processedBy', 'firstName lastName');

    if (!refund) {
      return res.status(404).json({ success: false, message: 'Refund not found' });
    }

    // Check authorization
    if (refund.user._id.toString() !== req.user._id.toString() && req.user.role !== 'admin') {
      return res.status(403).json({ success: false, message: 'Not authorized' });
    }

    res.json({ success: true, refund });
  } catch (error) {
    console.error('Error fetching refund:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

module.exports = router;
