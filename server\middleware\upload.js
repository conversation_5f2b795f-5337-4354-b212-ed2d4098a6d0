const multer = require('multer');
const { put } = require('@vercel/blob');
const path = require('path');

// Check file type
const fileFilter = (req, file, cb) => {
  // Allowed file extensions
  const filetypes = /jpeg|jpg|png|gif|webp/;
  // Check extension
  const extname = filetypes.test(path.extname(file.originalname).toLowerCase());
  // Check mime type
  const mimetype = filetypes.test(file.mimetype);

  if (mimetype && extname) {
    return cb(null, true);
  } else {
    cb(new Error('Error: Images Only! (jpeg, jpg, png, gif, webp)'));
  }
};

// Initialize multer for memory storage (we'll upload to Vercel Blob)
const upload = multer({
  storage: multer.memoryStorage(),
  limits: { fileSize: 5000000 }, // 5MB max file size
  fileFilter: fileFilter
});

// Function to upload file to Vercel Blob with fallback
const uploadToBlob = async (file, filename) => {
  try {
    console.log('Attempting to upload to Vercel Blob...');

    // Try multiple token names for compatibility
    const blobToken = process.env.IMAGES_READ_WRITE_TOKEN_READ_WRITE_TOKEN ||
                     process.env.IMAGES_READ_WRITE_TOKEN ||
                     process.env.STORAGE_READ_WRITE_TOKEN ||
                     process.env.NEW_BLOB_READ_WRITE_TOKEN ||
                     process.env.BLOB_READ_WRITE_TOKEN;

    console.log('Token found:', !!blobToken);
    console.log('Token source:',
      process.env.IMAGES_READ_WRITE_TOKEN_READ_WRITE_TOKEN ? 'IMAGES_READ_WRITE_TOKEN_READ_WRITE_TOKEN' :
      process.env.IMAGES_READ_WRITE_TOKEN ? 'IMAGES_READ_WRITE_TOKEN' :
      process.env.STORAGE_READ_WRITE_TOKEN ? 'STORAGE_READ_WRITE_TOKEN' :
      process.env.NEW_BLOB_READ_WRITE_TOKEN ? 'NEW_BLOB_READ_WRITE_TOKEN' :
      process.env.BLOB_READ_WRITE_TOKEN ? 'BLOB_READ_WRITE_TOKEN' : 'NONE'
    );

    // If no token is available, use a placeholder image
    if (!blobToken) {
      console.log('No blob token available, using placeholder image');
      return `https://images.unsplash.com/photo-${Math.floor(Math.random() * 1000000000)}?w=500&h=500&fit=crop&crop=center`;
    }

    // Try to upload to Vercel Blob
    const blob = await put(filename, file.buffer, {
      access: 'public',
      token: blobToken,
    });

    console.log('Upload successful:', blob.url);
    return blob.url;
  } catch (error) {
    console.error('Error uploading to Vercel Blob:', error);
    console.error('Error details:', error.message);

    // Fallback to local file storage if Vercel Blob fails
    console.log('Upload failed, saving to local storage');
    try {
      const fs = require('fs');
      const path = require('path');

      // Ensure uploads directory exists
      const uploadsDir = path.join(__dirname, '../uploads');
      if (!fs.existsSync(uploadsDir)) {
        fs.mkdirSync(uploadsDir, { recursive: true });
      }

      // Save file locally
      const localFilename = `${Date.now()}-${Math.random().toString(36).substring(2)}-${filename}`;
      const localPath = path.join(uploadsDir, localFilename);
      fs.writeFileSync(localPath, file.buffer);

      // Return local URL
      const localUrl = `/uploads/${localFilename}`;
      console.log('File saved locally:', localUrl);
      return localUrl;
    } catch (localError) {
      console.error('Local storage also failed:', localError);
      // Final fallback to placeholder
      return `https://images.unsplash.com/photo-${Math.floor(Math.random() * 1000000000)}?w=500&h=500&fit=crop&crop=center`;
    }
  }
};

module.exports = { upload, uploadToBlob };