const express = require('express');
const router = express.Router();
const Discount = require('../models/Discount');
const Product = require('../models/Product');
const { protect, admin } = require('../middleware/auth');

/**
 * @route   POST /api/discounts
 * @desc    Create discount code (Admin)
 * @access  Private/Admin
 */
router.post('/', protect, admin, async (req, res) => {
  try {
    const {
      name,
      code,
      description,
      type,
      value,
      minimumOrderAmount,
      maximumDiscountAmount,
      applicableProducts,
      applicableCategories,
      excludedProducts,
      usageLimit,
      validFrom,
      validUntil,
      isActive,
      isPublic,
      buyQuantity,
      getQuantity,
      getDiscountPercentage,
      freeShippingThreshold
    } = req.body;

    // Check if code already exists
    const existingDiscount = await Discount.findOne({ code: code.toUpperCase() });
    if (existingDiscount) {
      return res.status(400).json({ success: false, message: 'Discount code already exists' });
    }

    const discount = new Discount({
      name,
      code: code.toUpperCase(),
      description,
      type,
      value,
      minimumOrderAmount,
      maximumDiscountAmount,
      applicableProducts,
      applicableCategories,
      excludedProducts,
      usageLimit,
      validFrom,
      validUntil,
      isActive,
      isPublic,
      buyQuantity,
      getQuantity,
      getDiscountPercentage,
      freeShippingThreshold,
      createdBy: req.user._id
    });

    await discount.save();

    res.status(201).json({
      success: true,
      message: 'Discount created successfully',
      discount
    });
  } catch (error) {
    console.error('Error creating discount:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

/**
 * @route   POST /api/discounts/validate
 * @desc    Validate discount code for customer use
 * @access  Private (Customer)
 */
router.post('/validate', protect, async (req, res) => {
  try {
    const { code, orderAmount, items } = req.body;
    const userId = req.user._id;

    console.log('🔍 Validating discount code:', { code, orderAmount, userId });

    // Find the discount code
    const discount = await Discount.findOne({
      code: code.toUpperCase(),
      isActive: true
    });

    if (!discount) {
      return res.status(400).json({
        success: false,
        message: 'Invalid discount code'
      });
    }

    // Check if discount is currently valid (date range)
    const now = new Date();
    if (discount.validFrom > now || discount.validUntil < now) {
      return res.status(400).json({
        success: false,
        message: 'This discount code has expired or is not yet active'
      });
    }

    // Check usage limits
    if (discount.usageLimit.total && discount.usageCount >= discount.usageLimit.total) {
      return res.status(400).json({
        success: false,
        message: 'This discount code has reached its usage limit'
      });
    }

    // Check per-user usage limit
    const userUsage = discount.userUsage.find(u => u.user.toString() === userId.toString());
    if (userUsage && userUsage.count >= discount.usageLimit.perUser) {
      return res.status(400).json({
        success: false,
        message: 'You have already used this discount code the maximum number of times'
      });
    }

    // Check minimum order amount
    if (orderAmount < discount.minimumOrderAmount) {
      return res.status(400).json({
        success: false,
        message: `Minimum order amount of $${discount.minimumOrderAmount} required for this discount`
      });
    }

    // Calculate discount amount
    let discountAmount = 0;

    if (discount.type === 'percentage') {
      discountAmount = (orderAmount * discount.value) / 100;

      // Apply maximum discount limit if set
      if (discount.maximumDiscountAmount && discountAmount > discount.maximumDiscountAmount) {
        discountAmount = discount.maximumDiscountAmount;
      }
    } else if (discount.type === 'fixed_amount') {
      discountAmount = Math.min(discount.value, orderAmount);
    } else if (discount.type === 'free_shipping') {
      // For free shipping, we'll handle this differently
      discountAmount = 0; // Shipping cost would be handled separately
    }

    console.log('✅ Discount validated successfully:', {
      code: discount.code,
      type: discount.type,
      value: discount.value,
      discountAmount: discountAmount
    });

    res.json({
      success: true,
      message: 'Discount code applied successfully',
      discount: {
        _id: discount._id,
        name: discount.name,
        code: discount.code,
        description: discount.description,
        type: discount.type,
        value: discount.value
      },
      discountAmount: Math.round(discountAmount * 100) / 100 // Round to 2 decimal places
    });

  } catch (error) {
    console.error('❌ Error validating discount:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while validating discount code'
    });
  }
});

/**
 * @route   GET /api/discounts
 * @desc    Get all discounts (Admin)
 * @access  Private/Admin
 */
router.get('/', protect, admin, async (req, res) => {
  try {
    const { status, type, page = 1, limit = 20 } = req.query;
    
    let filter = {};
    if (status === 'active') {
      filter.isActive = true;
      filter.validFrom = { $lte: new Date() };
      filter.validUntil = { $gte: new Date() };
    } else if (status === 'inactive') {
      filter.isActive = false;
    } else if (status === 'expired') {
      filter.validUntil = { $lt: new Date() };
    }
    
    if (type) {
      filter.type = type;
    }

    const discounts = await Discount.find(filter)
      .populate('createdBy', 'firstName lastName')
      .populate('applicableProducts', 'name')
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await Discount.countDocuments(filter);

    res.json({
      success: true,
      discounts,
      pagination: {
        page: parseInt(page),
        pages: Math.ceil(total / limit),
        total
      }
    });
  } catch (error) {
    console.error('Error fetching discounts:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

/**
 * @route   POST /api/discounts/validate
 * @desc    Validate discount code
 * @access  Private
 */
router.post('/validate', protect, async (req, res) => {
  try {
    const { code, orderAmount, items } = req.body;

    const discount = await Discount.findOne({ 
      code: code.toUpperCase(),
      isActive: true 
    }).populate('applicableProducts excludedProducts');

    if (!discount) {
      return res.status(404).json({ success: false, message: 'Invalid discount code' });
    }

    // Check if discount is valid
    if (!discount.isValid()) {
      return res.status(400).json({ success: false, message: 'Discount code has expired or reached usage limit' });
    }

    // Check if user can use this discount
    if (!discount.canUserUse(req.user._id)) {
      return res.status(400).json({ success: false, message: 'You have reached the usage limit for this discount' });
    }

    // Check minimum order amount
    if (orderAmount < discount.minimumOrderAmount) {
      return res.status(400).json({ 
        success: false, 
        message: `Minimum order amount of $${discount.minimumOrderAmount} required` 
      });
    }

    // Calculate discount amount
    let discountAmount = 0;
    let applicableAmount = orderAmount;

    // Filter applicable items
    if (discount.applicableProducts.length > 0 || discount.applicableCategories.length > 0) {
      applicableAmount = items.reduce((sum, item) => {
        const isApplicable = 
          discount.applicableCategories.includes(item.category) ||
          discount.applicableProducts.some(p => p._id.toString() === item.productId);
        
        const isExcluded = 
          discount.excludedProducts.some(p => p._id.toString() === item.productId);
        
        return sum + (isApplicable && !isExcluded ? item.price * item.quantity : 0);
      }, 0);
    }

    // Calculate discount based on type
    switch (discount.type) {
      case 'percentage':
        discountAmount = (applicableAmount * discount.value) / 100;
        if (discount.maximumDiscountAmount) {
          discountAmount = Math.min(discountAmount, discount.maximumDiscountAmount);
        }
        break;
      case 'fixed_amount':
        discountAmount = Math.min(discount.value, applicableAmount);
        break;
      case 'free_shipping':
        discountAmount = 0; // Handled separately in shipping calculation
        break;
      case 'buy_x_get_y':
        // Complex logic for buy X get Y discounts
        discountAmount = calculateBuyXGetYDiscount(discount, items);
        break;
    }

    res.json({
      success: true,
      discount: {
        id: discount._id,
        name: discount.name,
        code: discount.code,
        type: discount.type,
        discountAmount: Math.round(discountAmount * 100) / 100
      }
    });
  } catch (error) {
    console.error('Error validating discount:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

/**
 * @route   PUT /api/discounts/:id
 * @desc    Update discount (Admin)
 * @access  Private/Admin
 */
router.put('/:id', protect, admin, async (req, res) => {
  try {
    const discount = await Discount.findById(req.params.id);
    
    if (!discount) {
      return res.status(404).json({ success: false, message: 'Discount not found' });
    }

    // Update fields
    Object.keys(req.body).forEach(key => {
      if (key !== '_id' && key !== 'createdBy' && key !== 'createdAt') {
        discount[key] = req.body[key];
      }
    });

    await discount.save();

    res.json({
      success: true,
      message: 'Discount updated successfully',
      discount
    });
  } catch (error) {
    console.error('Error updating discount:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

/**
 * @route   DELETE /api/discounts/:id
 * @desc    Delete discount (Admin)
 * @access  Private/Admin
 */
router.delete('/:id', protect, admin, async (req, res) => {
  try {
    const discount = await Discount.findById(req.params.id);
    
    if (!discount) {
      return res.status(404).json({ success: false, message: 'Discount not found' });
    }

    await discount.deleteOne();

    res.json({
      success: true,
      message: 'Discount deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting discount:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

// Helper function for buy X get Y discounts
function calculateBuyXGetYDiscount(discount, items) {
  // Implementation for buy X get Y logic
  // This is a simplified version - you can enhance based on specific requirements
  let discountAmount = 0;
  
  if (discount.applicableProducts.length > 0) {
    const applicableItems = items.filter(item => 
      discount.applicableProducts.some(p => p._id.toString() === item.productId)
    );
    
    applicableItems.forEach(item => {
      const setsOfX = Math.floor(item.quantity / discount.buyQuantity);
      const freeItems = setsOfX * discount.getQuantity;
      const discountPerItem = item.price * (discount.getDiscountPercentage / 100);
      discountAmount += freeItems * discountPerItem;
    });
  }
  
  return discountAmount;
}

module.exports = router;
