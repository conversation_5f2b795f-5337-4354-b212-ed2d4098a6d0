import { ProductCard } from './ProductCard';
import { Product } from '../types';

interface ProductGridProps {
  products: Product[];
  onViewDetails: (product: Product) => void;
}

export function ProductGrid({ products, onViewDetails }: ProductGridProps) {
  if (products.length === 0) {
    return (
      <div className="text-center py-20">
        <div className="text-green-400 mb-6">
          <svg className="mx-auto h-32 w-32" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8l-4 4-4-4" />
          </svg>
        </div>
        <h3 className="text-2xl font-bold text-gray-900 mb-4">No products found</h3>
        <p className="text-gray-600 text-lg">Try adjusting your search or filter criteria</p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4 lg:gap-6">
      {products.map((product, index) => (
        <div
          key={product.id}
          className="animate-scale-in"
          style={{
            animationDelay: `${index * 0.1}s`,
            animationFillMode: 'both'
          }}
        >
          <ProductCard
            product={product}
            onViewDetails={onViewDetails}
          />
        </div>
      ))}
    </div>
  );
}