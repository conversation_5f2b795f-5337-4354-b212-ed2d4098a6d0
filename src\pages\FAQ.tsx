import React, { useState } from 'react';
import { ChevronDown, ChevronUp } from 'lucide-react';

export function FAQ() {
  const [openItems, setOpenItems] = useState<number[]>([]);

  const toggleItem = (index: number) => {
    setOpenItems(prev => 
      prev.includes(index) 
        ? prev.filter(i => i !== index)
        : [...prev, index]
    );
  };

  const faqCategories = [
    {
      title: 'Orders & Payment',
      questions: [
        {
          question: 'How do I place an order?',
          answer: 'Simply browse our products, add items to your cart, and proceed to checkout. You\'ll need to provide shipping information and payment details to complete your order.'
        },
        {
          question: 'What payment methods do you accept?',
          answer: 'We accept all major credit cards (Visa, MasterCard, American Express), PayPal, and Apple Pay for secure online payments.'
        },
        {
          question: 'Can I modify or cancel my order?',
          answer: 'You can modify or cancel your order within 1 hour of placing it. After that, please contact our customer service team for assistance.'
        },
        {
          question: 'Do you offer price matching?',
          answer: 'We don\'t currently offer price matching, but we strive to provide competitive prices and regular promotions for our customers.'
        },
        {
          question: 'How do I apply a discount code?',
          answer: 'Enter your discount code in the "Promo Code" field during checkout. The discount will be applied automatically before payment.'
        },
        {
          question: 'Can I save items for later?',
          answer: 'Yes! You can add items to your wishlist by clicking the heart icon on any product. Access your wishlist anytime from the header menu.'
        }
      ]
    },
    {
      title: 'Shipping & Delivery',
      questions: [
        {
          question: 'How long does shipping take?',
          answer: 'Standard shipping takes 5-7 business days, express shipping takes 2-3 business days, and overnight shipping delivers in 1 business day.'
        },
        {
          question: 'Do you ship internationally?',
          answer: 'Yes, we ship to most countries worldwide. International shipping typically takes 7-14 business days and costs are calculated at checkout.'
        },
        {
          question: 'How can I track my order?',
          answer: 'Once your order ships, you\'ll receive a tracking number via email. You can track your package on our website or directly with the shipping carrier.'
        },
        {
          question: 'What if my package is lost or damaged?',
          answer: 'If your package is lost or damaged during shipping, please contact us immediately. We\'ll work with the carrier to resolve the issue.'
        },
        {
          question: 'Do you offer free shipping?',
          answer: 'Yes! We offer free standard shipping on orders over $50. Express and overnight shipping options are available for an additional fee.'
        },
        {
          question: 'Can I change my shipping address after placing an order?',
          answer: 'You can change your shipping address within 1 hour of placing your order. After that, please contact customer service as soon as possible.'
        }
      ]
    },
    {
      title: 'Returns & Exchanges',
      questions: [
        {
          question: 'What is your return policy?',
          answer: 'We offer a 30-day return policy for unused items in original condition. Items must be returned with all tags attached and original packaging.'
        },
        {
          question: 'How do I return an item?',
          answer: 'Contact our customer service team to initiate a return. We\'ll provide you with a prepaid return shipping label and instructions.'
        },
        {
          question: 'How long does it take to process a refund?',
          answer: 'Refunds are processed within 5-7 business days after we receive your returned item. The refund will be credited to your original payment method.'
        },
        {
          question: 'Do you offer exchanges?',
          answer: 'We don\'t offer direct exchanges. To exchange an item, please return the original item for a refund and place a new order.'
        }
      ]
    },
    {
      title: 'Account & Technical',
      questions: [
        {
          question: 'Do I need an account to place an order?',
          answer: 'No, you can checkout as a guest. However, creating an account allows you to track orders, save addresses, and view order history.'
        },
        {
          question: 'How do I reset my password?',
          answer: 'Click "Forgot Password" on the login page and enter your email address. We\'ll send you instructions to reset your password.'
        },
        {
          question: 'Is my personal information secure?',
          answer: 'Yes, we use industry-standard encryption to protect your personal and payment information. We never store credit card details on our servers.'
        },
        {
          question: 'Why can\'t I add items to my cart?',
          answer: 'This could be due to browser cookies being disabled or the item being out of stock. Try refreshing the page or clearing your browser cache.'
        }
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-br from-green-900 via-green-800 to-green-900 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 sm:py-24 lg:py-32">
          <div className="text-center">
            <div className="inline-flex items-center px-6 py-3 rounded-full bg-white/10 backdrop-blur-md border border-white/20 mb-8">
              <ChevronDown className="h-5 w-5 mr-3 text-green-300" />
              <span className="text-sm font-semibold">Help Center</span>
            </div>
            <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold mb-6">Frequently Asked Questions</h1>
            <p className="text-lg sm:text-xl md:text-2xl text-green-100 max-w-4xl mx-auto leading-relaxed">
              Find answers to common questions about orders, shipping, returns, and more. Get the help you need quickly and easily.
            </p>
          </div>
        </div>
      </div>

      {/* FAQ Content */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          {faqCategories.map((category, categoryIndex) => (
            <div key={categoryIndex} className="mb-12">
              <div className="text-center mb-8">
                <h2 className="text-3xl font-bold text-gray-900 mb-4">{category.title}</h2>
                <div className="w-20 h-1 bg-gradient-to-r from-green-500 to-emerald-500 mx-auto rounded-full"></div>
              </div>
              <div className="space-y-4">
                {category.questions.map((faq, questionIndex) => {
                  const itemIndex = categoryIndex * 100 + questionIndex;
                  const isOpen = openItems.includes(itemIndex);

                  return (
                    <div key={questionIndex} className="bg-white border border-gray-200 rounded-2xl shadow-sm hover:shadow-md transition-all duration-300">
                      <button
                        onClick={() => toggleItem(itemIndex)}
                        className="w-full px-6 py-5 text-left flex items-center justify-between hover:bg-gray-50 transition-colors rounded-2xl"
                      >
                        <span className="font-semibold text-gray-900 text-lg">{faq.question}</span>
                        <div className={`p-2 rounded-full transition-all duration-300 ${isOpen ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-500'}`}>
                          {isOpen ? (
                            <ChevronUp className="h-5 w-5" />
                          ) : (
                            <ChevronDown className="h-5 w-5" />
                          )}
                        </div>
                      </button>
                      {isOpen && (
                        <div className="px-6 pb-5 border-t border-gray-100">
                          <p className="text-gray-600 leading-relaxed pt-4">{faq.answer}</p>
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            </div>
          ))}
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-16 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-gradient-to-r from-green-600 to-emerald-600 rounded-2xl p-8 text-white text-center">
            <div className="inline-flex p-4 bg-white/10 rounded-2xl mb-6">
              <ChevronDown className="h-8 w-8" />
            </div>
            <h2 className="text-3xl font-bold mb-4">Still Have Questions?</h2>
            <p className="text-lg text-green-100 mb-8 max-w-2xl mx-auto">
              Can't find what you're looking for? Our customer service team is here to help you 24/7.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="/support"
                className="bg-white text-green-600 px-8 py-4 rounded-xl font-semibold hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 shadow-lg"
              >
                Visit Support Center
              </a>
              <a
                href="/contact"
                className="border-2 border-white text-white px-8 py-4 rounded-xl font-semibold hover:bg-white hover:text-green-600 transition-all duration-300 transform hover:scale-105"
              >
                Contact Us
              </a>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
