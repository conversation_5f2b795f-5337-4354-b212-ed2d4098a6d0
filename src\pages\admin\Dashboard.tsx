import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import { 
  BarChart, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer,
  PieChart, Pie, Cell, AreaChart, Area
} from 'recharts';
import {
  ShoppingBag, DollarSign, Package, TrendingUp, AlertCircle,
  RefreshCw, Eye, TrendingDown, ArrowUpRight, RotateCcw, Percent, Receipt, MapPin, BarChart3
} from 'lucide-react';
import AdminLayout from './components/AdminLayout';
import { API_URL } from '../../utils/env';

interface Stats {
  totalUsers: number;
  totalOrders: number;
  totalProducts: number;
  totalRevenue: number;
  grossSales: number;
  netSales: number;
  averageOrderValue: number;
  totalRefunds: number;
  refundCount: number;
  refundRate: number | undefined;
  totalTaxCollected: number;
  dailyRevenue: Array<{ date: string; amount: number }>;
  monthlySales: Array<{ month: number; year: number; amount: number; orders: number }>;
  topSellingProducts: Array<{
    product: any;
    quantity: number;
    revenue: number;
    productDetails: {
      name: string;
      image: string;
      price: number;
      category: string;
    };
  }>;
  lowPerformingProducts: Array<{
    product: any;
    quantity: number;
    revenue: number;
    productDetails: {
      name: string;
      image: string;
      price: number;
      category: string;
    };
  }>;
  salesByCategory: Array<{ category: string; sales: number }>;
  salesByBrand: Array<{ brand: string; sales: number }>;
  salesByLocation: Array<{ location: string; sales: number }>;
  salesByProvince: Array<{ province: string; sales: number }>;
  taxByProvince: Array<{ province: string; tax: number }>;
  topCategories: Array<{ category: string; sales: number }>;
  recentOrders: Array<any>;
  lowStockProducts: Array<any>;
  period: string;
  dateRange: {
    start: string | null;
    end: string | null;
  };
}

const Dashboard: React.FC = () => {
  const { token } = useAuth();
  const [stats, setStats] = useState<Stats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [timeframe, setTimeframe] = useState<'daily' | 'monthly'>('daily');
  const [period, setPeriod] = useState<'daily' | 'weekly' | 'monthly' | 'custom' | 'all'>('all');
  const [customDateRange, setCustomDateRange] = useState({ start: '', end: '' });
  const [activeView, setActiveView] = useState<'overview' | 'products' | 'sales'>('overview');

  useEffect(() => {
    fetchStats();
  }, [token, period, customDateRange]);

  const fetchStats = async () => {
    try {
      setLoading(true);
      let url = `${API_URL}/api/stats?period=${period}`;
      
      if (period === 'custom' && customDateRange.start && customDateRange.end) {
        url += `&startDate=${customDateRange.start}&endDate=${customDateRange.end}`;
      }
      
      const response = await fetch(url, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch statistics');
      }

      const data = await response.json();
      setStats(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const refreshStats = async () => {
    try {
      setLoading(true);
      const response = await fetch(`${API_URL}/api/stats/refresh`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to refresh statistics');
      }

      await fetchStats();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatPercentage = (value: number | undefined | null) => {
    if (value === undefined || value === null || isNaN(value)) {
      return '0.0%';
    }
    return `${value.toFixed(1)}%`;
  };

  const prepareChartData = () => {
    if (!stats) return [];

    if (timeframe === 'daily') {
      return stats.dailyRevenue
        .slice(-7)
        .map(item => ({
          name: new Date(item.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
          revenue: item.amount,
        }));
    } else {
      return stats.monthlySales
        .slice(-6)
        .map(item => ({
          name: new Date(item.year, item.month).toLocaleDateString('en-US', { month: 'short', year: 'numeric' }),
          revenue: item.amount,
          orders: item.orders,
        }));
    }
  };

  const prepareCategoryPieData = () => {
    if (!stats || !stats.salesByCategory) return [];
    return stats.salesByCategory.slice(0, 6).map((item, index) => ({
      name: item.category,
      value: item.sales,
      color: `hsl(${(index * 60) % 360}, 70%, 50%)`
    }));
  };

  const COLORS = ['#10b981', '#3b82f6', '#8b5cf6', '#f59e0b', '#ef4444', '#06b6d4'];

  if (loading && !stats) {
    return (
      <AdminLayout>
        <div className="flex flex-col justify-center items-center h-96">
          <div className="animate-spin rounded-full h-16 w-16 border-4 border-green-200 border-t-green-600 mb-4"></div>
          <p className="text-gray-600 font-medium">Loading comprehensive analytics...</p>
        </div>
      </AdminLayout>
    );
  }

  if (error) {
    return (
      <AdminLayout>
        <div className="bg-gradient-to-r from-red-50 to-pink-50 border-l-4 border-red-500 p-6 mb-6 rounded-xl shadow-lg">
          <div className="flex items-center">
            <div className="w-10 h-10 bg-red-500 rounded-xl flex items-center justify-center mr-4">
              <AlertCircle className="h-6 w-6 text-white" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-red-800">Error Loading Dashboard</h3>
              <p className="text-red-700 mt-1">{error}</p>
            </div>
          </div>
          <button
            onClick={fetchStats}
            className="mt-4 px-6 py-3 bg-red-600 text-white rounded-xl hover:bg-red-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105"
          >
            Try Again
          </button>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      {/* Header */}
      <div className="mb-8 flex flex-col lg:flex-row justify-between items-start lg:items-center space-y-4 lg:space-y-0">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center">
            <span className="w-10 h-10 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center mr-4 shadow-lg">
              <span className="text-white text-lg">📊</span>
            </span>
            Sales Analytics Dashboard
          </h1>
          <p className="text-gray-600 mt-2">Comprehensive insights into your store performance</p>
        </div>
        
        {/* Controls */}
        <div className="flex flex-wrap gap-4 items-center">
          {/* Period Selector */}
          <div className="flex bg-white rounded-xl p-1 shadow-md border border-gray-200">
            {['all', 'daily', 'weekly', 'monthly', 'custom'].map((p) => (
              <button
                key={p}
                onClick={() => setPeriod(p as any)}
                className={`px-4 py-2 rounded-lg font-medium text-sm transition-all duration-200 capitalize ${
                  period === p ? 'bg-green-600 text-white shadow-md' : 'text-gray-600 hover:text-green-600 hover:bg-green-50'
                }`}
              >
                {p}
              </button>
            ))}
          </div>
          
          {/* Custom Date Range */}
          {period === 'custom' && (
            <div className="flex gap-2 items-center bg-white p-2 rounded-xl shadow-md border border-gray-200">
              <input
                type="date"
                value={customDateRange.start}
                onChange={(e) => setCustomDateRange(prev => ({ ...prev, start: e.target.value }))}
                className="px-3 py-2 border border-gray-300 rounded-lg text-sm"
              />
              <span className="text-gray-500">to</span>
              <input
                type="date"
                value={customDateRange.end}
                onChange={(e) => setCustomDateRange(prev => ({ ...prev, end: e.target.value }))}
                className="px-3 py-2 border border-gray-300 rounded-lg text-sm"
              />
            </div>
          )}
          
          <button
            onClick={refreshStats}
            disabled={loading}
            className="px-6 py-3 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-xl hover:from-green-700 hover:to-emerald-700 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center shadow-lg hover:shadow-xl transform hover:scale-105"
          >
            {loading ? (
              <span className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></span>
            ) : (
              <RefreshCw className="h-5 w-5 mr-2" />
            )}
            Refresh
          </button>
        </div>
      </div>

      {/* View Tabs */}
      <div className="mb-8">
        <div className="flex space-x-1 bg-gray-100 p-1 rounded-xl">
          {[
            { key: 'overview', label: 'Sales Overview', icon: TrendingUp },
            { key: 'products', label: 'Product Performance', icon: Package },
            { key: 'sales', label: 'Sales Breakdown', icon: BarChart }
          ].map(({ key, label, icon: Icon }) => (
            <button
              key={key}
              onClick={() => setActiveView(key as any)}
              className={`flex items-center px-6 py-3 rounded-lg font-medium text-sm transition-all duration-200 ${
                activeView === key 
                  ? 'bg-white text-green-600 shadow-md' 
                  : 'text-gray-600 hover:text-green-600'
              }`}
            >
              <Icon className="h-5 w-5 mr-2" />
              {label}
            </button>
          ))}
        </div>
      </div>

      {stats && (
        <>
          {/* Sales Overview */}
          {activeView === 'overview' && (
            <div className="space-y-8">
              {/* Key Metrics Cards */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {/* Total Sales */}
                <div className="bg-gradient-to-br from-white to-green-50/50 rounded-2xl shadow-lg p-6 border border-green-100 hover:shadow-xl transition-all duration-300">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600 font-semibold uppercase tracking-wide">Total Sales</p>
                      <p className="text-3xl font-bold text-gray-900 mt-1">{formatCurrency(stats.totalRevenue)}</p>
                      <p className="text-xs text-green-600 font-medium mt-1 flex items-center">
                        <ArrowUpRight className="h-3 w-3 mr-1" />
                        +15% from last period
                      </p>
                    </div>
                    <div className="rounded-2xl bg-gradient-to-br from-green-500 to-emerald-600 p-4 shadow-lg">
                      <DollarSign className="h-7 w-7 text-white" />
                    </div>
                  </div>
                </div>

                {/* Gross vs Net Sales */}
                <div className="bg-gradient-to-br from-white to-blue-50/50 rounded-2xl shadow-lg p-6 border border-blue-100 hover:shadow-xl transition-all duration-300">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600 font-semibold uppercase tracking-wide">Gross Sales</p>
                      <p className="text-2xl font-bold text-gray-900 mt-1">{formatCurrency(stats.grossSales)}</p>
                      <p className="text-xs text-gray-500 mt-1">Net: {formatCurrency(stats.netSales)}</p>
                    </div>
                    <div className="rounded-2xl bg-gradient-to-br from-blue-500 to-blue-600 p-4 shadow-lg">
                      <TrendingUp className="h-7 w-7 text-white" />
                    </div>
                  </div>
                </div>

                {/* Number of Orders */}
                <div className="bg-gradient-to-br from-white to-purple-50/50 rounded-2xl shadow-lg p-6 border border-purple-100 hover:shadow-xl transition-all duration-300">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600 font-semibold uppercase tracking-wide">Total Orders</p>
                      <p className="text-3xl font-bold text-gray-900 mt-1">{stats.totalOrders}</p>
                      <p className="text-xs text-purple-600 font-medium mt-1 flex items-center">
                        <ArrowUpRight className="h-3 w-3 mr-1" />
                        +8% from last period
                      </p>
                    </div>
                    <div className="rounded-2xl bg-gradient-to-br from-purple-500 to-purple-600 p-4 shadow-lg">
                      <ShoppingBag className="h-7 w-7 text-white" />
                    </div>
                  </div>
                </div>

                {/* Average Order Value */}
                <div className="bg-gradient-to-br from-white to-orange-50/50 rounded-2xl shadow-lg p-6 border border-orange-100 hover:shadow-xl transition-all duration-300">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600 font-semibold uppercase tracking-wide">Avg Order Value</p>
                      <p className="text-3xl font-bold text-gray-900 mt-1">{formatCurrency(stats.averageOrderValue)}</p>
                      <p className="text-xs text-orange-600 font-medium mt-1 flex items-center">
                        <ArrowUpRight className="h-3 w-3 mr-1" />
                        +5% from last period
                      </p>
                    </div>
                    <div className="rounded-2xl bg-gradient-to-br from-orange-500 to-orange-600 p-4 shadow-lg">
                      <Percent className="h-7 w-7 text-white" />
                    </div>
                  </div>
                </div>
              </div>

              {/* Refunds/Returns Section */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div className="bg-gradient-to-br from-white to-red-50/50 rounded-2xl shadow-lg p-6 border border-red-100">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600 font-semibold uppercase tracking-wide">Total Refunds</p>
                      <p className="text-2xl font-bold text-gray-900 mt-1">{formatCurrency(stats.totalRefunds)}</p>
                    </div>
                    <div className="rounded-2xl bg-gradient-to-br from-red-500 to-red-600 p-4 shadow-lg">
                      <RotateCcw className="h-6 w-6 text-white" />
                    </div>
                  </div>
                </div>
                
                <div className="bg-gradient-to-br from-white to-red-50/50 rounded-2xl shadow-lg p-6 border border-red-100">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600 font-semibold uppercase tracking-wide">Refund Count</p>
                      <p className="text-2xl font-bold text-gray-900 mt-1">{stats.refundCount}</p>
                    </div>
                    <div className="rounded-2xl bg-gradient-to-br from-red-500 to-red-600 p-4 shadow-lg">
                      <Package className="h-6 w-6 text-white" />
                    </div>
                  </div>
                </div>
                
                <div className="bg-gradient-to-br from-white to-red-50/50 rounded-2xl shadow-lg p-6 border border-red-100">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600 font-semibold uppercase tracking-wide">Refund Rate</p>
                      <p className="text-2xl font-bold text-gray-900 mt-1">{formatPercentage(stats.refundRate)}</p>
                    </div>
                    <div className="rounded-2xl bg-gradient-to-br from-red-500 to-red-600 p-4 shadow-lg">
                      <TrendingDown className="h-6 w-6 text-white" />
                    </div>
                  </div>
                </div>
                
                <div className="bg-gradient-to-br from-white to-green-50/50 rounded-2xl shadow-lg p-6 border border-green-100">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600 font-semibold uppercase tracking-wide">Tax Collected</p>
                      <p className="text-2xl font-bold text-gray-900 mt-1">{formatCurrency(stats.totalTaxCollected)}</p>
                    </div>
                    <div className="rounded-2xl bg-gradient-to-br from-green-500 to-green-600 p-4 shadow-lg">
                      <Receipt className="h-6 w-6 text-white" />
                    </div>
                  </div>
                </div>
              </div>

              {/* Revenue Chart */}
              <div className="bg-gradient-to-br from-white to-green-50/30 rounded-2xl shadow-xl p-8 border border-green-100">
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8 space-y-4 sm:space-y-0">
                  <div>
                    <h2 className="text-2xl font-bold text-gray-900 flex items-center">
                      <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center mr-3">
                        <TrendingUp className="h-5 w-5 text-white" />
                      </div>
                      Revenue Trends
                    </h2>
                    <p className="text-gray-600 mt-1">Track your sales performance over time</p>
                  </div>
                  <div className="flex space-x-2 bg-white rounded-xl p-1 shadow-md border border-green-200">
                    <button
                      onClick={() => setTimeframe('daily')}
                      className={`px-4 py-2 rounded-lg font-medium text-sm transition-all duration-200 ${
                        timeframe === 'daily' ? 'bg-green-600 text-white shadow-md' : 'text-gray-600 hover:text-green-600 hover:bg-green-50'
                      }`}
                    >
                      Daily
                    </button>
                    <button
                      onClick={() => setTimeframe('monthly')}
                      className={`px-4 py-2 rounded-lg font-medium text-sm transition-all duration-200 ${
                        timeframe === 'monthly' ? 'bg-green-600 text-white shadow-md' : 'text-gray-600 hover:text-green-600 hover:bg-green-50'
                      }`}
                    >
                      Monthly
                    </button>
                  </div>
                </div>

                <div className="h-96 bg-white rounded-xl p-4 shadow-inner border border-gray-100">
                  <ResponsiveContainer width="100%" height="100%">
                    <AreaChart data={prepareChartData()} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                      <defs>
                        <linearGradient id="colorRevenue" x1="0" y1="0" x2="0" y2="1">
                          <stop offset="5%" stopColor="#10b981" stopOpacity={0.8}/>
                          <stop offset="95%" stopColor="#10b981" stopOpacity={0.1}/>
                        </linearGradient>
                      </defs>
                      <CartesianGrid strokeDasharray="3 3" vertical={false} stroke="#e5e7eb" />
                      <XAxis dataKey="name" tick={{ fontSize: 12 }} stroke="#6b7280" />
                      <YAxis tickFormatter={(value: number) => `$${value}`} tick={{ fontSize: 12 }} stroke="#6b7280" />
                      <Tooltip
                        formatter={(value: number) => [`$${value}`, 'Revenue']}
                        contentStyle={{
                          backgroundColor: 'white',
                          border: '1px solid #d1fae5',
                          borderRadius: '12px',
                          boxShadow: '0 10px 25px rgba(0,0,0,0.1)'
                        }}
                      />
                      <Area type="monotone" dataKey="revenue" stroke="#10b981" fillOpacity={1} fill="url(#colorRevenue)" strokeWidth={3} />
                    </AreaChart>
                  </ResponsiveContainer>
                </div>
              </div>
            </div>
          )}

          {/* Product Performance */}
          {activeView === 'products' && (
            <div className="space-y-8">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Top Selling Products */}
                <div className="bg-gradient-to-br from-white to-green-50/30 rounded-2xl shadow-xl p-8 border border-green-100">
                  <div className="flex items-center mb-6">
                    <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center mr-3">
                      <TrendingUp className="h-5 w-5 text-white" />
                    </div>
                    <h2 className="text-xl font-bold text-gray-900">Top Selling Products</h2>
                  </div>
                  <div className="space-y-4">
                    {stats.topSellingProducts && stats.topSellingProducts.slice(0, 5).map((product, index) => (
                      <div key={index} className="flex items-center justify-between p-4 bg-white rounded-xl shadow-sm border border-green-100 hover:shadow-md transition-all duration-200">
                        <div className="flex items-center">
                          <img
                            src={product.productDetails?.image || '/placeholder-product.svg'}
                            alt={product.productDetails?.name || 'Product'}
                            className="h-12 w-12 rounded-xl object-cover mr-4 shadow-md border border-green-200"
                            onError={(e) => {
                              const target = e.target as HTMLImageElement;
                              target.src = '/placeholder-product.svg';
                            }}
                          />
                          <div>
                            <div className="text-sm font-semibold text-gray-900">{product.productDetails?.name || 'Unknown Product'}</div>
                            <div className="text-xs text-gray-500">{product.productDetails?.category || 'Unknown Category'}</div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-sm font-bold text-gray-900">{formatCurrency(product.revenue || 0)}</div>
                          <div className="text-xs text-green-600">{product.quantity || 0} sold</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Low Performing Products */}
                <div className="bg-gradient-to-br from-white to-red-50/30 rounded-2xl shadow-xl p-8 border border-red-100">
                  <div className="flex items-center mb-6">
                    <div className="w-8 h-8 bg-gradient-to-br from-red-500 to-red-600 rounded-xl flex items-center justify-center mr-3">
                      <TrendingDown className="h-5 w-5 text-white" />
                    </div>
                    <h2 className="text-xl font-bold text-gray-900">Low Performing Products</h2>
                  </div>
                  <div className="space-y-4">
                    {stats.lowPerformingProducts && stats.lowPerformingProducts.slice(0, 5).map((product, index) => (
                      <div key={index} className="flex items-center justify-between p-4 bg-white rounded-xl shadow-sm border border-red-100 hover:shadow-md transition-all duration-200">
                        <div className="flex items-center">
                          <img
                            src={product.productDetails?.image || '/placeholder-product.svg'}
                            alt={product.productDetails?.name || 'Product'}
                            className="h-12 w-12 rounded-xl object-cover mr-4 shadow-md border border-red-200"
                            onError={(e) => {
                              const target = e.target as HTMLImageElement;
                              target.src = '/placeholder-product.svg';
                            }}
                          />
                          <div>
                            <div className="text-sm font-semibold text-gray-900">{product.productDetails?.name || 'Unknown Product'}</div>
                            <div className="text-xs text-gray-500">{product.productDetails?.category || 'Unknown Category'}</div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-sm font-bold text-gray-900">{formatCurrency(product.revenue || 0)}</div>
                          <div className="text-xs text-red-600">{product.quantity || 0} sold</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Sales Breakdown */}
          {activeView === 'sales' && (
            <div className="space-y-8">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Sales by Category */}
                <div className="bg-gradient-to-br from-white to-blue-50/30 rounded-2xl shadow-xl p-8 border border-blue-100">
                  <div className="flex items-center mb-6">
                    <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mr-3">
                      <Package className="h-5 w-5 text-white" />
                    </div>
                    <h2 className="text-xl font-bold text-gray-900">Sales by Category</h2>
                  </div>
                  
                  <div className="h-64 mb-6">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={prepareCategoryPieData()}
                          cx="50%"
                          cy="50%"
                          outerRadius={80}
                          fill="#8884d8"
                          dataKey="value"
                          label={({ name, percent }) => `${name} ${percent ? (percent * 100).toFixed(0) : 0}%`}
                        >
                          {prepareCategoryPieData().map((_, index) => (
                            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                          ))}
                        </Pie>
                        <Tooltip formatter={(value: number) => formatCurrency(value)} />
                      </PieChart>
                    </ResponsiveContainer>
                  </div>
                  
                  <div className="space-y-3">
                    {stats.salesByCategory && stats.salesByCategory.slice(0, 6).map((category, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <div className="flex items-center">
                          <div 
                            className="w-4 h-4 rounded-full mr-3" 
                            style={{ backgroundColor: COLORS[index % COLORS.length] }}
                          ></div>
                          <span className="text-sm font-medium text-gray-900">{category.category}</span>
                        </div>
                        <span className="text-sm font-bold text-gray-900">{formatCurrency(category.sales)}</span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Sales by Brand */}
                <div className="bg-gradient-to-br from-white to-purple-50/30 rounded-2xl shadow-xl p-8 border border-purple-100">
                  <div className="flex items-center mb-6">
                    <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mr-3">
                      <Package className="h-5 w-5 text-white" />
                    </div>
                    <h2 className="text-xl font-bold text-gray-900">Sales by Brand</h2>
                  </div>
                  
                  <div className="space-y-4">
                    {stats.salesByBrand && stats.salesByBrand.slice(0, 8).map((brand, index) => (
                      <div key={index} className="flex items-center justify-between p-4 bg-white rounded-xl shadow-sm border border-purple-100">
                        <div className="flex items-center">
                          <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mr-4">
                            <span className="text-white font-bold text-sm">{brand.brand?.charAt(0) || 'B'}</span>
                          </div>
                          <span className="text-sm font-medium text-gray-900">{brand.brand || 'Unknown Brand'}</span>
                        </div>
                        <span className="text-sm font-bold text-gray-900">{formatCurrency(brand.sales || 0)}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
              
              {/* Geographic Sales Analysis */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Sales by Province */}
                <div className="bg-gradient-to-br from-white to-indigo-50/30 rounded-2xl shadow-xl p-8 border border-indigo-100">
                  <div className="flex items-center mb-6">
                    <div className="w-8 h-8 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-xl flex items-center justify-center mr-3">
                      <MapPin className="h-5 w-5 text-white" />
                    </div>
                    <h2 className="text-xl font-bold text-gray-900">Sales by Province</h2>
                  </div>
                  
                  <div className="space-y-4">
                    {stats.salesByProvince && stats.salesByProvince.slice(0, 8).map((province, index) => (
                      <div key={index} className="flex items-center justify-between p-4 bg-white rounded-xl shadow-sm border border-indigo-100">
                        <div className="flex items-center">
                          <div className="w-10 h-10 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-xl flex items-center justify-center mr-4">
                            <span className="text-white font-bold text-sm">{province.province?.substring(0, 2).toUpperCase() || 'PR'}</span>
                          </div>
                          <span className="text-sm font-medium text-gray-900">{province.province || 'Unknown Province'}</span>
                        </div>
                        <span className="text-sm font-bold text-gray-900">{formatCurrency(province.sales || 0)}</span>
                      </div>
                    ))}
                  </div>
                </div>
                
                {/* Tax by Province */}
                <div className="bg-gradient-to-br from-white to-emerald-50/30 rounded-2xl shadow-xl p-8 border border-emerald-100">
                  <div className="flex items-center mb-6">
                    <div className="w-8 h-8 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-xl flex items-center justify-center mr-3">
                      <Receipt className="h-5 w-5 text-white" />
                    </div>
                    <h2 className="text-xl font-bold text-gray-900">Tax by Province</h2>
                  </div>
                  
                  <div className="space-y-4">
                    {stats.taxByProvince && stats.taxByProvince.slice(0, 8).map((province, index) => (
                      <div key={index} className="flex items-center justify-between p-4 bg-white rounded-xl shadow-sm border border-emerald-100">
                        <div className="flex items-center">
                          <div className="w-10 h-10 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-xl flex items-center justify-center mr-4">
                            <span className="text-white font-bold text-sm">{province.province?.substring(0, 2).toUpperCase() || 'PR'}</span>
                          </div>
                          <span className="text-sm font-medium text-gray-900">{province.province || 'Unknown Province'}</span>
                        </div>
                        <span className="text-sm font-bold text-gray-900">{formatCurrency(province.tax || 0)}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Quick Analytics Access */}
          <div className="bg-gradient-to-br from-white to-blue-50/30 rounded-2xl shadow-xl p-8 border border-blue-100">
            <div className="flex items-center mb-6">
              <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mr-3">
                <BarChart3 className="h-5 w-5 text-white" />
              </div>
              <h2 className="text-xl font-bold text-gray-900">Advanced Analytics</h2>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Link
                to="/admin/analytics?tab=overview"
                className="p-4 bg-white rounded-xl shadow-sm border border-gray-200 hover:shadow-md hover:border-blue-300 transition-all duration-200 group"
              >
                <div className="flex items-center">
                  <div className="w-10 h-10 bg-green-100 rounded-xl flex items-center justify-center mr-3 group-hover:bg-green-200 transition-colors">
                    <DollarSign className="h-5 w-5 text-green-600" />
                  </div>
                  <div>
                    <div className="font-semibold text-gray-900 text-sm">Sales Overview</div>
                    <div className="text-xs text-gray-500">Revenue & trends</div>
                  </div>
                </div>
              </Link>

              <Link
                to="/admin/analytics?tab=products"
                className="p-4 bg-white rounded-xl shadow-sm border border-gray-200 hover:shadow-md hover:border-blue-300 transition-all duration-200 group"
              >
                <div className="flex items-center">
                  <div className="w-10 h-10 bg-purple-100 rounded-xl flex items-center justify-center mr-3 group-hover:bg-purple-200 transition-colors">
                    <Package className="h-5 w-5 text-purple-600" />
                  </div>
                  <div>
                    <div className="font-semibold text-gray-900 text-sm">Product Performance</div>
                    <div className="text-xs text-gray-500">Top & low performers</div>
                  </div>
                </div>
              </Link>

              <Link
                to="/admin/analytics?tab=geography"
                className="p-4 bg-white rounded-xl shadow-sm border border-gray-200 hover:shadow-md hover:border-blue-300 transition-all duration-200 group"
              >
                <div className="flex items-center">
                  <div className="w-10 h-10 bg-orange-100 rounded-xl flex items-center justify-center mr-3 group-hover:bg-orange-200 transition-colors">
                    <MapPin className="h-5 w-5 text-orange-600" />
                  </div>
                  <div>
                    <div className="font-semibold text-gray-900 text-sm">Geographic Insights</div>
                    <div className="text-xs text-gray-500">Sales by location</div>
                  </div>
                </div>
              </Link>

              <Link
                to="/admin/analytics?tab=tax"
                className="p-4 bg-white rounded-xl shadow-sm border border-gray-200 hover:shadow-md hover:border-blue-300 transition-all duration-200 group"
              >
                <div className="flex items-center">
                  <div className="w-10 h-10 bg-indigo-100 rounded-xl flex items-center justify-center mr-3 group-hover:bg-indigo-200 transition-colors">
                    <Receipt className="h-5 w-5 text-indigo-600" />
                  </div>
                  <div>
                    <div className="font-semibold text-gray-900 text-sm">Tax Reports</div>
                    <div className="text-xs text-gray-500">Tax collection & export</div>
                  </div>
                </div>
              </Link>
            </div>

            <div className="mt-6 text-center">
              <Link
                to="/admin/analytics"
                className="inline-flex items-center text-blue-600 hover:text-blue-700 text-sm font-semibold bg-blue-50 hover:bg-blue-100 px-6 py-3 rounded-xl transition-all duration-200"
              >
                <BarChart3 className="h-4 w-4 mr-2" />
                View Full Analytics Dashboard
              </Link>
            </div>
          </div>

          {/* Recent Orders Section */}
          <div className="bg-gradient-to-br from-white to-gray-50/30 rounded-2xl shadow-xl p-8 border border-gray-100">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center">
                <div className="w-8 h-8 bg-gradient-to-br from-gray-500 to-gray-600 rounded-xl flex items-center justify-center mr-3">
                  <ShoppingBag className="h-5 w-5 text-white" />
                </div>
                <h2 className="text-xl font-bold text-gray-900">Recent Orders</h2>
              </div>
              <Link
                to="/admin/orders"
                className="inline-flex items-center text-green-600 hover:text-green-700 text-sm font-semibold bg-green-50 hover:bg-green-100 px-4 py-2 rounded-xl transition-all duration-200"
              >
                <Eye className="h-4 w-4 mr-2" />
                View all orders
              </Link>
            </div>
            
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gradient-to-r from-gray-50 to-gray-100">
                  <tr>
                    <th className="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">Order ID</th>
                    <th className="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">Customer</th>
                    <th className="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">Amount</th>
                    <th className="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">Status</th>
                    <th className="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">Date</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-100">
                  {stats.recentOrders.slice(0, 5).map((order) => (
                    <tr key={order._id} className="hover:bg-gray-50 transition-colors duration-200">
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        #{order._id.substring(order._id.length - 6)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {order.user?.firstName} {order.user?.lastName}
                        </div>
                        <div className="text-xs text-gray-500">{order.user?.email}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900">
                        {formatCurrency(order.total)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span
                          className={`px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                            order.status === 'delivered'
                              ? 'bg-green-100 text-green-800'
                              : order.status === 'processing'
                              ? 'bg-yellow-100 text-yellow-800'
                              : order.status === 'cancelled'
                              ? 'bg-red-100 text-red-800'
                              : 'bg-blue-100 text-blue-800'
                          }`}
                        >
                          {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {new Date(order.createdAt).toLocaleDateString('en-US', {
                            year: 'numeric',
                            month: '2-digit',
                            day: '2-digit'
                          })}
                        </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </>
      )}
    </AdminLayout>
  );
};

export default Dashboard;