{"name": "jaisalgo-api", "version": "1.0.0", "description": "API for JaisalGo Online", "main": "index.js", "type": "commonjs", "scripts": {"start": "node index.js", "dev": "nodemon index.js"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "body-parser": "^1.20.2", "dotenv": "^16.3.1", "mongoose": "^7.5.0", "stripe": "^13.5.0", "multer": "^1.4.5-lts.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "nodemailer": "^6.9.4"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=18.0.0"}}