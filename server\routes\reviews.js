const express = require('express');
const {
  getReviews,
  getReview,
  addReview,
  updateReview,
  deleteReview,
  getProductReviews,
  recalculateAllRatings
} = require('../controllers/reviewController');

const router = express.Router({ mergeParams: true });

const { protect, admin } = require('../middleware/auth');

router
  .route('/')
  .get(getProductReviews)
  .post(protect, addReview);

router
  .route('/:id')
  .get(getReview)
  .put(protect, updateReview)
  .delete(protect, deleteReview);

// Utility route to recalculate all ratings (temporarily public to fix data)
router.post('/recalculate-all', recalculateAllRatings);

module.exports = router;