import React, { useState, useEffect } from 'react';
import { ArrowLeft, Package, Clock, CheckCircle, XCircle, RefreshCw } from 'lucide-react';
import { useNavigate, useParams } from 'react-router-dom';
import { API_URL } from '../../utils/env';

interface OrderItem {
  _id: string;
  product: {
    _id: string;
    name: string;
    image: string;
  };
  name: string;
  price: number;
  quantity: number;
}

interface Order {
  _id: string;
  orderNumber: string;
  items: OrderItem[];
  total: number;
  status: string;
  createdAt: string;
}

interface RefundRequest {
  _id: string;
  order: {
    orderNumber: string;
  };
  items: Array<{
    product: {
      name: string;
      image: string;
    };
    quantity: number;
    price: number;
    reason: string;
  }>;
  refundAmount: number;
  status: string;
  reason: string;
  customerNotes?: string;
  adminNotes?: string;
  createdAt: string;
  estimatedProcessingDays: number;
}

const RefundRequest: React.FC = () => {
  const { orderId } = useParams<{ orderId: string }>();
  const navigate = useNavigate();
  const [order, setOrder] = useState<Order | null>(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [showForm, setShowForm] = useState(false);
  const [myRefunds, setMyRefunds] = useState<RefundRequest[]>([]);
  
  const [refundItems, setRefundItems] = useState<Array<{
    productId: string;
    quantity: number;
    reason: string;
  }>>([]);
  const [refundReason, setRefundReason] = useState('');
  const [customerNotes, setCustomerNotes] = useState('');

  const token = localStorage.getItem('token');

  useEffect(() => {
    if (orderId) {
      fetchOrder();
    } else {
      fetchMyRefunds();
    }
  }, [orderId]);

  const fetchOrder = async () => {
    try {
      setLoading(true);
      const response = await fetch(`${API_URL}/api/orders/${orderId}`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      if (!response.ok) {
        throw new Error('Failed to fetch order');
      }
      
      const data = await response.json();
      console.log('Order data received:', data);
      console.log('Order items:', data.items);
      setOrder(data);

      // Initialize refund items
      setRefundItems(data.items.map((item: OrderItem) => {
        console.log('Processing item:', item);
        const productId = item.product?._id || item.product || (item as any).productId;
        console.log('Product ID:', productId);
        return {
          productId: productId,
          quantity: 0,
          reason: ''
        };
      }));
    } catch (error) {
      console.error('Error fetching order:', error);
      alert('Failed to load order details');
    } finally {
      setLoading(false);
    }
  };

  const fetchMyRefunds = async () => {
    try {
      setLoading(true);
      const response = await fetch(`${API_URL}/api/refunds/my-requests`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      if (!response.ok) {
        throw new Error('Failed to fetch refunds');
      }
      
      const data = await response.json();
      setMyRefunds(data.refunds || []);
    } catch (error) {
      console.error('Error fetching refunds:', error);
      alert('Failed to load refund requests');
    } finally {
      setLoading(false);
    }
  };

  const updateRefundItem = (productId: string, field: string, value: any) => {
    setRefundItems(prev => prev.map(item => 
      item.productId === productId 
        ? { ...item, [field]: value }
        : item
    ));
  };

  const submitRefundRequest = async () => {
    try {
      setSubmitting(true);
      
      const itemsToRefund = refundItems.filter(item => item.quantity > 0);
      
      if (itemsToRefund.length === 0) {
        alert('Please select at least one item to refund');
        return;
      }
      
      if (!refundReason.trim()) {
        alert('Please provide a reason for the refund');
        return;
      }

      console.log('Submitting refund request:', {
        orderId: order?._id,
        items: itemsToRefund,
        reason: refundReason,
        customerNotes,
        orderStatus: order?.status
      });

      const response = await fetch(`${API_URL}/api/refunds/request`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`
        },
        body: JSON.stringify({
          orderId: order?._id,
          items: itemsToRefund,
          reason: refundReason,
          customerNotes
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Refund request failed:', errorData);
        throw new Error(errorData.message || 'Failed to submit refund request');
      }
      
      alert('Refund request submitted successfully! We will review it within 2-3 business days.');
      navigate('/account?tab=orders');
    } catch (error) {
      console.error('Error submitting refund:', error);
      alert(error instanceof Error ? error.message : 'Failed to submit refund request');
    } finally {
      setSubmitting(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'text-yellow-600';
      case 'approved': return 'text-green-600';
      case 'rejected': return 'text-red-600';
      case 'processing': return 'text-green-600';
      case 'completed': return 'text-gray-600';
      default: return 'text-gray-600';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return <Clock className="h-5 w-5" />;
      case 'approved': return <CheckCircle className="h-5 w-5" />;
      case 'rejected': return <XCircle className="h-5 w-5" />;
      case 'processing': return <RefreshCw className="h-5 w-5" />;
      case 'completed': return <CheckCircle className="h-5 w-5" />;
      default: return <Clock className="h-5 w-5" />;
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <RefreshCw className="h-8 w-8 animate-spin text-green-600" />
      </div>
    );
  }

  // Show refund history if no specific order
  if (!orderId) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4">
          <div className="flex items-center mb-6">
            <button
              onClick={() => navigate('/account?tab=orders')}
              className="flex items-center text-green-600 hover:text-green-800"
            >
              <ArrowLeft className="h-5 w-5 mr-2" />
              Back to Orders
            </button>
          </div>

          <h1 className="text-2xl font-bold text-gray-900 mb-6">My Refund Requests</h1>

          {myRefunds.length === 0 ? (
            <div className="bg-white rounded-lg shadow-sm p-8 text-center">
              <Package className="h-12 w-12 mx-auto mb-4 text-gray-400" />
              <p className="text-gray-500">No refund requests found</p>
            </div>
          ) : (
            <div className="space-y-4">
              {myRefunds.map((refund) => (
                <div key={refund._id} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                  <div className="flex justify-between items-start mb-4">
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">
                        Order {refund.order.orderNumber}
                      </h3>
                      <p className="text-sm text-gray-500">
                        Requested on {new Date(refund.createdAt).toLocaleDateString()}
                      </p>
                    </div>
                    <div className={`flex items-center ${getStatusColor(refund.status)}`}>
                      {getStatusIcon(refund.status)}
                      <span className="ml-2 font-medium capitalize">{refund.status}</span>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                      <p className="text-sm text-gray-600">Refund Amount</p>
                      <p className="text-lg font-semibold text-gray-900">${refund.refundAmount.toFixed(2)}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Items</p>
                      <p className="text-sm text-gray-900">{refund.items.length} item(s)</p>
                    </div>
                  </div>

                  <div className="mb-4">
                    <p className="text-sm text-gray-600 mb-2">Reason</p>
                    <p className="text-sm text-gray-900">{refund.reason}</p>
                  </div>

                  {refund.adminNotes && (
                    <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                      <p className="text-sm text-green-800">
                        <strong>Admin Response:</strong> {refund.adminNotes}
                      </p>
                    </div>
                  )}

                  {refund.status === 'pending' && (
                    <div className="mt-4 text-sm text-gray-600">
                      <p>⏱️ Estimated processing time: {refund.estimatedProcessingDays} business days</p>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    );
  }

  // Show refund form for specific order
  if (!order) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-500">Order not found</p>
          <button
            onClick={() => navigate('/account?tab=orders')}
            className="mt-4 text-green-600 hover:text-green-800"
          >
            Back to Orders
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="flex items-center mb-6">
          <button
            onClick={() => navigate('/account?tab=orders')}
            className="flex items-center text-green-600 hover:text-green-800"
          >
            <ArrowLeft className="h-5 w-5 mr-2" />
            Back to Orders
          </button>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Request Refund</h1>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div>
              <p className="text-sm text-gray-600">Order Number</p>
              <p className="font-semibold text-gray-900">{order.orderNumber}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Order Date</p>
              <p className="font-semibold text-gray-900">
                {new Date(order.createdAt).toLocaleDateString()}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Order Total</p>
              <p className="font-semibold text-gray-900">${order.total.toFixed(2)}</p>
            </div>
          </div>

          {!showForm ? (
            <div>
              <p className="text-gray-600 mb-4">
                You can request a refund for items from this order. Please note that refunds are subject to our return policy.
              </p>
              <button
                onClick={() => setShowForm(true)}
                className="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors"
              >
                Start Refund Request
              </button>
            </div>
          ) : (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Select Items to Refund</h3>
                <div className="space-y-4">
                  {order.items.map((item) => {
                    const productId = item.product?._id || item.product || (item as any).productId;
                    const refundItem = refundItems.find(ri => ri.productId === productId);
                    return (
                      <div key={item._id} className="flex items-center space-x-4 p-4 border border-gray-200 rounded-lg">
                        <img
                          src={item.product?.image || '/placeholder-product.svg'}
                          alt={item.name}
                          className="w-16 h-16 object-cover rounded"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.src = '/placeholder-product.svg';
                          }}
                        />
                        <div className="flex-1">
                          <h4 className="font-medium text-gray-900">{item.name}</h4>
                          <p className="text-sm text-gray-600">
                            ${item.price.toFixed(2)} × {item.quantity} = ${(item.price * item.quantity).toFixed(2)}
                          </p>
                        </div>
                        <div className="flex items-center space-x-4">
                          <div>
                            <label className="block text-sm text-gray-600 mb-1">Quantity</label>
                            <select
                              value={refundItem?.quantity || 0}
                              onChange={(e) => updateRefundItem(productId, 'quantity', parseInt(e.target.value))}
                              className="px-3 py-1 border border-gray-300 rounded focus:ring-2 focus:ring-green-500 focus:border-transparent"
                            >
                              {Array.from({ length: item.quantity + 1 }, (_, i) => (
                                <option key={i} value={i}>{i}</option>
                              ))}
                            </select>
                          </div>
                          {(refundItem?.quantity || 0) > 0 && (
                            <div>
                              <label className="block text-sm text-gray-600 mb-1">Reason</label>
                              <select
                                value={refundItem?.reason || ''}
                                onChange={(e) => updateRefundItem(productId, 'reason', e.target.value)}
                                className="px-3 py-1 border border-gray-300 rounded focus:ring-2 focus:ring-green-500 focus:border-transparent"
                                required
                              >
                                <option value="">Select reason</option>
                                <option value="defective">Defective</option>
                                <option value="wrong_item">Wrong item</option>
                                <option value="not_as_described">Not as described</option>
                                <option value="damaged">Damaged</option>
                                <option value="changed_mind">Changed mind</option>
                                <option value="other">Other</option>
                              </select>
                            </div>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Overall Reason for Refund *
                </label>
                <textarea
                  value={refundReason}
                  onChange={(e) => setRefundReason(e.target.value)}
                  rows={3}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  placeholder="Please explain why you're requesting this refund..."
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Additional Notes (Optional)
                </label>
                <textarea
                  value={customerNotes}
                  onChange={(e) => setCustomerNotes(e.target.value)}
                  rows={2}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  placeholder="Any additional information..."
                />
              </div>

              <div className="flex space-x-4">
                <button
                  onClick={() => setShowForm(false)}
                  className="px-6 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={submitRefundRequest}
                  disabled={submitting}
                  className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50"
                >
                  {submitting ? 'Submitting...' : 'Submit Refund Request'}
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default RefundRequest;
