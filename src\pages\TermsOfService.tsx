import { FileText, ShoppingCart, Shield, AlertTriangle, Scale, Mail, Phone, MapPin } from 'lucide-react';

export function TermsOfService() {
  const sections = [
    {
      id: 'acceptance',
      title: 'Acceptance of Terms',
      icon: FileText,
      content: [
        {
          subtitle: 'Agreement to Terms',
          text: 'By accessing and using jaisalgoonline website, you accept and agree to be bound by the terms and provision of this agreement. If you do not agree to abide by the above, please do not use this service.'
        },
        {
          subtitle: 'Modifications',
          text: 'We reserve the right to change these terms and conditions at any time. Your continued use of the website following any changes shall constitute your acceptance of such changes.'
        },
        {
          subtitle: 'Eligibility',
          text: 'You must be at least 18 years old to use our services. By using our website, you represent and warrant that you meet this age requirement and have the legal capacity to enter into this agreement.'
        }
      ]
    },
    {
      id: 'use-of-website',
      title: 'Use of Website',
      icon: Shield,
      content: [
        {
          subtitle: 'Permitted Use',
          text: 'You may use our website for lawful purposes only. You agree not to use the website in any way that could damage, disable, overburden, or impair our servers or networks, or interfere with any other party\'s use of the website.'
        },
        {
          subtitle: 'Prohibited Activities',
          text: 'You may not use our website to transmit, distribute, store or destroy material that could constitute or encourage conduct that would be considered a criminal offense, give rise to civil liability, or otherwise violate any law or regulation.'
        },
        {
          subtitle: 'Account Security',
          text: 'You are responsible for maintaining the confidentiality of your account information and password. You agree to notify us immediately of any unauthorized use of your account.'
        }
      ]
    },
    {
      id: 'products-orders',
      title: 'Products and Orders',
      icon: ShoppingCart,
      content: [
        {
          subtitle: 'Product Information',
          text: 'We strive to provide accurate product descriptions, images, and pricing. However, we do not warrant that product descriptions or other content is accurate, complete, reliable, current, or error-free.'
        },
        {
          subtitle: 'Order Acceptance',
          text: 'All orders are subject to acceptance by us. We reserve the right to refuse or cancel any order for any reason, including but not limited to product availability, errors in product or pricing information, or suspected fraudulent activity.'
        },
        {
          subtitle: 'Pricing and Payment',
          text: 'All prices are subject to change without notice. Payment must be received by us before we ship any products. We accept various payment methods as displayed during checkout.'
        }
      ]
    },
    {
      id: 'liability',
      title: 'Limitation of Liability',
      icon: AlertTriangle,
      content: [
        {
          subtitle: 'Disclaimer of Warranties',
          text: 'Our website and services are provided "as is" without any representations or warranties, express or implied. We make no representations or warranties in relation to this website or the information and materials provided.'
        },
        {
          subtitle: 'Limitation of Damages',
          text: 'In no event shall jaisalgoonline be liable for any indirect, incidental, special, consequential, or punitive damages, including without limitation, loss of profits, data, use, goodwill, or other intangible losses.'
        },
        {
          subtitle: 'Maximum Liability',
          text: 'Our total liability to you for all damages, losses, and causes of action shall not exceed the amount paid by you, if any, for accessing or using our website or purchasing products from us.'
        }
      ]
    }
  ];

  const contactInfo = [
    {
      icon: Mail,
      label: 'Email',
      value: '<EMAIL>',
      link: 'mailto:<EMAIL>'
    },
    {
      icon: Phone,
      label: 'Phone',
      value: '+****************',
      link: 'tel:+***********'
    },
    {
      icon: MapPin,
      label: 'Address',
      value: '123 Commerce Street, Business District, NY 10001',
      link: null
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="py-12 lg:py-16 bg-gradient-to-br from-green-600 to-emerald-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-white/20 rounded-2xl mb-6">
            <Scale className="h-8 w-8" />
          </div>
          <h1 className="text-4xl lg:text-5xl font-bold mb-6">Terms of Service</h1>
          <p className="text-xl text-green-100 max-w-3xl mx-auto leading-relaxed">
            Please read these terms carefully before using our website and services. These terms govern your use of jaisalgoonline.
          </p>
          <div className="mt-6 text-sm text-green-200">
            Last updated: {new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })}
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="py-16 lg:py-20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Introduction */}
          <div className="bg-white rounded-3xl shadow-lg p-8 lg:p-10 mb-12 border border-gray-100">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Introduction</h2>
            <div className="prose prose-lg text-gray-700 leading-relaxed">
              <p className="mb-4">
                Welcome to jaisalgoonline. These Terms of Service ("Terms") govern your use of our website and services.
                By accessing or using our website, you agree to be bound by these Terms and our Privacy Policy.
              </p>
              <p className="mb-4">
                These Terms constitute a legally binding agreement between you and jaisalgoonline. If you do not agree
                to these Terms, you may not access or use our website or services.
              </p>
              <p>
                We reserve the right to update these Terms at any time. We will notify you of any material changes by
                posting the new Terms on this page and updating the "Last updated" date.
              </p>
            </div>
          </div>

          {/* Terms Sections */}
          <div className="space-y-8">
            {sections.map((section, index) => (
              <div key={section.id} className="bg-white rounded-3xl shadow-lg overflow-hidden border border-gray-100">
                <div className="p-8 lg:p-10">
                  <div className="flex items-center mb-6">
                    <div className="inline-flex items-center justify-center w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-2xl mr-4">
                      <section.icon className="h-6 w-6" />
                    </div>
                    <h2 className="text-2xl font-bold text-gray-900">{section.title}</h2>
                  </div>

                  <div className="space-y-6">
                    {section.content.map((item, itemIndex) => (
                      <div key={itemIndex}>
                        <h3 className="text-lg font-semibold text-gray-900 mb-3">{item.subtitle}</h3>
                        <p className="text-gray-700 leading-relaxed">{item.text}</p>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Additional Terms */}
          <div className="bg-white rounded-3xl shadow-lg p-8 lg:p-10 mt-12 border border-gray-100">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Additional Terms</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Intellectual Property</h3>
                <p className="text-gray-700 leading-relaxed mb-4">
                  All content on this website, including text, graphics, logos, images, and software, is the property of
                  jaisalgoonline and is protected by copyright and other intellectual property laws.
                </p>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Shipping and Returns</h3>
                <p className="text-gray-700 leading-relaxed">
                  Shipping and return policies are detailed on our dedicated shipping and returns pages. By placing an order,
                  you agree to our shipping terms and return policy.
                </p>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Governing Law</h3>
                <p className="text-gray-700 leading-relaxed mb-4">
                  These Terms shall be governed by and construed in accordance with the laws of the State of New York,
                  without regard to its conflict of law provisions.
                </p>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Severability</h3>
                <p className="text-gray-700 leading-relaxed">
                  If any provision of these Terms is held to be invalid or unenforceable, the remaining provisions shall
                  remain in full force and effect.
                </p>
              </div>
            </div>
          </div>

          {/* Dispute Resolution */}
          <div className="bg-gradient-to-br from-amber-50 to-orange-50 rounded-3xl shadow-lg p-8 lg:p-10 mt-12 border border-amber-100">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Dispute Resolution</h2>
            <div className="space-y-4">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Informal Resolution</h3>
                <p className="text-gray-700 leading-relaxed">
                  Before filing a claim against jaisalgoonline, you agree to try to resolve the dispute informally by
                  contacting us. We'll try to resolve the dispute informally by contacting you via email.
                </p>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Binding Arbitration</h3>
                <p className="text-gray-700 leading-relaxed">
                  If we can't resolve the dispute informally, any dispute arising out of or relating to these Terms or
                  our services will be resolved through binding arbitration rather than in court.
                </p>
              </div>
            </div>
          </div>

          {/* Contact Section */}
          <div className="bg-gradient-to-br from-green-50 to-emerald-50 rounded-3xl shadow-lg p-8 lg:p-10 mt-12 border border-green-100">
            <div className="text-center mb-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">Questions About These Terms?</h2>
              <p className="text-gray-700 leading-relaxed">
                If you have any questions about these Terms of Service, please don't hesitate to contact our legal team.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {contactInfo.map((contact, index) => (
                <div key={index} className="text-center">
                  <div className="inline-flex items-center justify-center w-12 h-12 bg-green-600 text-white rounded-2xl mb-4">
                    <contact.icon className="h-6 w-6" />
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2">{contact.label}</h3>
                  {contact.link ? (
                    <a
                      href={contact.link}
                      className="text-green-600 hover:text-green-700 transition-colors font-medium"
                    >
                      {contact.value}
                    </a>
                  ) : (
                    <p className="text-gray-700">{contact.value}</p>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
