import React, { useState, useEffect } from 'react';
import { useAuth } from '../../context/AuthContext';
import { useToast } from '../../context/ToastContext';
import AdminLayout from './components/AdminLayout';
import { Edit, Trash2, Plus, Search, AlertCircle, Image as ImageIcon, Package } from 'lucide-react';
import { Product as BaseProduct } from '../../types';
import { API_URL } from '../../utils/env';

// Extended Product interface for admin functionality
interface Product extends BaseProduct {
  _id: string;
  additionalImages?: string[];
}

interface ProductsState {
  products: Product[];
  loading: boolean;
  error: string | null;
  currentPage: number;
  totalPages: number;
  searchTerm: string;
  selectedCategory: string;
  categories: string[];
}

interface ProductFormData {
  _id?: string;
  name: string;
  price: number;
  description: string;
  category: string;
  brand: string;
  inStock: boolean;
  image: File | null;
  additionalImages: File[];
  featured: boolean;
  imagePreview: string;
  additionalImagePreviews: string[];
  quantity?: number;
  originalPrice?: number;
  // Discount fields
  discountType?: 'none' | 'percentage' | 'fixed';
  discountValue?: number;
  discountStartDate?: string;
  discountEndDate?: string;
  // Tax fields
  taxRate?: number;
  taxIncluded?: boolean;
  selectedTaxRule?: string;
  selectedDiscountTemplate?: string;
}

const Products: React.FC = () => {
  const { token } = useAuth();
  const { showToast } = useToast();
  const [state, setState] = useState<ProductsState>({
    products: [],
    loading: true,
    error: null,
    currentPage: 1,
    totalPages: 1,
    searchTerm: '',
    selectedCategory: '',
    categories: [],
  });

  const [showForm, setShowForm] = useState(false);
  const [availableTaxRules, setAvailableTaxRules] = useState<any[]>([]);
  const [availableDiscounts, setAvailableDiscounts] = useState<any[]>([]);
  const [formData, setFormData] = useState<ProductFormData>({
    name: '',
    price: 0,
    description: '',
    category: '',
    brand: '',
    inStock: true,
    image: null,
    additionalImages: [],
    featured: false,
    imagePreview: '',
    additionalImagePreviews: [],
    quantity: 0,
    originalPrice: 0,
    // Discount fields
    discountType: 'none',
    discountValue: 0,
    discountStartDate: '',
    discountEndDate: '',
    // Tax fields
    taxRate: 0,
    taxIncluded: false
  });
  const [isEditing, setIsEditing] = useState(false);
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [deleteConfirm, setDeleteConfirm] = useState<string | null>(null);

  useEffect(() => {
    fetchProducts();
    fetchCategories();
    fetchTaxRules();
    fetchDiscounts();
  }, [token, state.currentPage, state.searchTerm, state.selectedCategory]);

  // Fetch tax rules and discounts for better UX
  const fetchTaxRules = async () => {
    try {
      const response = await fetch(`${API_URL}/api/taxes`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      if (response.ok) {
        const data = await response.json();
        setAvailableTaxRules(data.taxRules || []);
      }
    } catch (error) {
      console.error('Error fetching tax rules:', error);
    }
  };

  const fetchDiscounts = async () => {
    try {
      const response = await fetch(`${API_URL}/api/discounts`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      if (response.ok) {
        const data = await response.json();
        setAvailableDiscounts(data.discounts || []);
      }
    } catch (error) {
      console.error('Error fetching discounts:', error);
    }
  };

  // Auto-apply tax rule based on product category or location
  const autoApplyTaxRule = (category: string) => {
    const applicableRule = availableTaxRules.find(rule =>
      rule.isActive && (
        rule.applicableCategories?.includes(category) ||
        rule.name.toLowerCase().includes('standard') ||
        rule.name.toLowerCase().includes('default')
      )
    );

    if (applicableRule) {
      setFormData(prev => ({
        ...prev,
        taxRate: applicableRule.rate,
        taxIncluded: false
      }));
      console.log(`Auto-applied tax rule: ${applicableRule.name} (${applicableRule.rate}%)`);
    }
  };

  const fetchProducts = async () => {
    try {
      setState(prev => ({ ...prev, loading: true }));
      let url = `${API_URL}/api/products?page=${state.currentPage}`;
      
      if (state.searchTerm) {
        url += `&search=${encodeURIComponent(state.searchTerm)}`;
      }
      
      if (state.selectedCategory) {
        url += `&category=${encodeURIComponent(state.selectedCategory)}`;
      }

      const response = await fetch(url, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch products');
      }

      const data = await response.json();
      
      // Process product images to ensure proper URLs
      const processedProducts = data.products.map((product: any) => ({
        ...product,
        image: product.image ? 
          (product.image.startsWith('http') || product.image.startsWith('/uploads/') ? 
            product.image : 
            `${API_URL}${product.image}`) : 
          '',
        additionalImages: (product.images || []).map((img: string) => 
          img ? (img.startsWith('http') || img.startsWith('/uploads/') ? img : `${API_URL}${img}`) : ''
        ).filter(Boolean)
      }));
      
      setState(prev => ({
        ...prev,
        products: processedProducts,
        totalPages: data.totalPages,
        loading: false,
      }));
    } catch (err) {
      setState(prev => ({
        ...prev,
        error: err instanceof Error ? err.message : 'An error occurred',
        loading: false,
      }));
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await fetch(`${API_URL}/api/products/categories/all`);
      
      // Get the response text first
      const responseText = await response.text();
      
      if (!response.ok) {
        throw new Error('Failed to fetch categories');
      }
      
      // Parse the response text as JSON
      try {
        const data: string[] = JSON.parse(responseText);
        setState(prev => ({ ...prev, categories: data }));
      } catch (parseError) {
        console.error('Error parsing categories response:', parseError);
        throw new Error(`Unexpected token '<', "<!doctype "... is not valid JSON`);
      }
    } catch (err) {
      console.error('Error fetching categories:', err);
      setState(prev => ({
        ...prev,
        error: err instanceof Error ? err.message : 'An error occurred',
      }));
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setState(prev => ({ ...prev, currentPage: 1 }));
  };

  const handleCategoryChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setState(prev => ({
      ...prev,
      selectedCategory: e.target.value,
      currentPage: 1,
    }));
  };

  const handlePageChange = (page: number) => {
    setState(prev => ({ ...prev, currentPage: page }));
  };

  const resetForm = () => {
    setFormData({
      name: '',
      price: 0,
      description: '',
      category: '',
      brand: '',
      inStock: true,
      image: null,
      additionalImages: [],
      featured: false,
      imagePreview: '',
      additionalImagePreviews: [],
      quantity: 0,
      originalPrice: 0,
    });
    setFormErrors({});
    setIsEditing(false);
  };

  const handleAddProduct = () => {
    resetForm();
    setShowForm(true);
  };

  const handleEditProduct = (product: Product) => {
    // Ensure image URLs are properly formatted
    const imageUrl = product.image ? 
      (product.image.startsWith('http') || product.image.startsWith('/uploads/') ? 
        product.image : 
        `${API_URL}${product.image}`) : 
      '';
    
    const additionalImageUrls = (product.additionalImages || []).map(img => 
      img ? (img.startsWith('http') || img.startsWith('/uploads/') ? img : `${API_URL}${img}`) : ''
    ).filter(Boolean);
    
    setFormData({
      _id: product._id,
      name: product.name,
      price: product.price,
      description: product.description || '',
      category: product.category || '',
      brand: (product as any).brand || '',
      inStock: product.inStock,
      image: null,
      additionalImages: [],
      featured: product.featured || false,
      imagePreview: imageUrl,
      additionalImagePreviews: additionalImageUrls,
      quantity: product.quantity || 0,
      originalPrice: product.originalPrice || 0,
    });
    setIsEditing(true);
    setShowForm(true);
  };

  const validateForm = () => {
    const errors: Record<string, string> = {};
    if (!formData.name.trim()) errors.name = 'Name is required';
    if (formData.price <= 0) errors.price = 'Price must be greater than 0';
    if (!formData.description.trim()) errors.description = 'Description is required';
    if (!formData.category.trim()) errors.category = 'Category is required';
    if (!formData.brand.trim()) errors.brand = 'Brand is required';
    // inStock is now a boolean, no validation needed
    if (!isEditing && !formData.image) errors.image = 'Product image is required';
    
    // Original price is now calculated automatically, no validation needed
    
    if (formData.quantity !== undefined && formData.quantity < 0) {
      errors.quantity = 'Quantity cannot be negative';
    }
    
    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;
    
    try {
      const formDataToSend = new FormData();
      formDataToSend.append('name', formData.name);
      formDataToSend.append('price', formData.price.toString());
      formDataToSend.append('description', formData.description);
      formDataToSend.append('category', formData.category);
      formDataToSend.append('brand', formData.brand);
      formDataToSend.append('inStock', formData.inStock.toString());
      formDataToSend.append('featured', formData.featured.toString());
      
      if (formData.quantity !== undefined) {
        formDataToSend.append('quantity', formData.quantity.toString());
      }
      
      // Calculate and send original price if discount is applied
      if (formData.discountType === 'percentage' && formData.discountValue > 0 && formData.price > 0) {
        const calculatedOriginalPrice = formData.price / (1 - formData.discountValue / 100);
        formDataToSend.append('originalPrice', calculatedOriginalPrice.toString());
      } else if (formData.originalPrice !== undefined && formData.originalPrice > 0) {
        formDataToSend.append('originalPrice', formData.originalPrice.toString());
      }

      // Add discount fields
      if (formData.discountType) {
        formDataToSend.append('discountType', formData.discountType);
      }
      if (formData.discountValue !== undefined) {
        formDataToSend.append('discountValue', formData.discountValue.toString());
      }
      if (formData.discountStartDate) {
        formDataToSend.append('discountStartDate', formData.discountStartDate);
      }
      if (formData.discountEndDate) {
        formDataToSend.append('discountEndDate', formData.discountEndDate);
      }

      // Add tax fields
      if (formData.taxRate !== undefined) {
        formDataToSend.append('taxRate', formData.taxRate.toString());
      }
      if (formData.taxIncluded !== undefined) {
        formDataToSend.append('taxIncluded', formData.taxIncluded.toString());
      }

      if (formData.image) {
        formDataToSend.append('image', formData.image);
      }
      
      formData.additionalImages.forEach(image => {
        formDataToSend.append('additionalImages', image);
      });
      
      const url = isEditing 
        ? `${API_URL}/api/products/${formData._id}` 
        : `${API_URL}/api/products`;
      
      const method = isEditing ? 'PUT' : 'POST';
      
      const response = await fetch(url, {
        method,
        headers: {
          Authorization: `Bearer ${token}`,
        },
        body: formDataToSend,
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to save product');
      }
      
      setShowForm(false);
      resetForm();
      fetchProducts();
    } catch (err) {
      setState(prev => ({
        ...prev,
        error: err instanceof Error ? err.message : 'An error occurred',
      }));
    }
  };

  const handleDeleteProduct = async (id: string) => {
    try {
      const response = await fetch(`${API_URL}/api/products/${id}`, {
        method: 'DELETE',
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      
      if (!response.ok) {
        throw new Error('Failed to delete product');
      }
      
      setDeleteConfirm(null);
      fetchProducts();
    } catch (err) {
      setState(prev => ({
        ...prev,
        error: err instanceof Error ? err.message : 'An error occurred',
      }));
    }
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];

      // Validate file type
      if (!file.type.startsWith('image/')) {
        alert('Please select a valid image file');
        return;
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        alert('Image size must be less than 5MB');
        return;
      }

      const imageUrl = URL.createObjectURL(file);

      setFormData(prev => ({
        ...prev,
        image: file,
        imagePreview: imageUrl,
      }));

      console.log('Image selected successfully');
    }
  };

  const handleAdditionalImagesChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const filesArray = Array.from(e.target.files);
      const newPreviews = filesArray.map(file => URL.createObjectURL(file));
      
      setFormData(prev => ({
        ...prev,
        additionalImages: [...prev.additionalImages, ...filesArray],
        additionalImagePreviews: [...prev.additionalImagePreviews, ...newPreviews],
      }));
    }
  };

  const removeAdditionalImage = (index: number) => {
    setFormData(prev => {
      const newAdditionalImages = [...prev.additionalImages];
      const newAdditionalImagePreviews = [...prev.additionalImagePreviews];
      
      newAdditionalImages.splice(index, 1);
      newAdditionalImagePreviews.splice(index, 1);
      
      return {
        ...prev,
        additionalImages: newAdditionalImages,
        additionalImagePreviews: newAdditionalImagePreviews,
      };
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  return (
    <AdminLayout>
      <div className="mb-6 flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">Products</h1>
        <button
          onClick={handleAddProduct}
          className="px-6 py-3 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-xl hover:from-green-700 hover:to-emerald-700 transition-all duration-200 flex items-center shadow-lg hover:shadow-xl transform hover:scale-105"
        >
          <Plus className="h-5 w-5 mr-2" />
          Add Product
        </button>
      </div>

      {state.error && (
        <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-6">
          <div className="flex items-center">
            <AlertCircle className="h-6 w-6 text-red-500 mr-3" />
            <p className="text-red-700">{state.error}</p>
          </div>
        </div>
      )}

      {/* Search and Filter */}
      <div className="bg-white rounded-xl shadow-md p-6 mb-6">
        <div className="flex flex-col md:flex-row md:items-center md:space-x-4">
          <form onSubmit={handleSearch} className="flex-1 mb-4 md:mb-0">
            <div className="relative">
              <input
                type="text"
                placeholder="Search products..."
                className="w-full pl-10 pr-4 py-3 border-2 border-green-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200 bg-white hover:bg-green-50/30"
                value={state.searchTerm}
                onChange={(e) => setState(prev => ({ ...prev, searchTerm: e.target.value }))}
              />
              <Search className="absolute left-3 top-3.5 h-5 w-5 text-gray-400" />
              <button
                type="submit"
                className="absolute right-2 top-2.5 px-3 py-1 bg-green-100 text-green-700 rounded-lg text-sm hover:bg-green-200 transition-colors duration-200"
              >
                Search
              </button>
            </div>
          </form>

          <div className="w-full md:w-64">
            <select
              className="w-full px-4 py-3 border-2 border-green-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200 bg-white hover:bg-green-50/30"
              value={state.selectedCategory}
              onChange={handleCategoryChange}
            >
              <option value="">All Categories</option>
              <optgroup label="Popular Categories">
                <option value="Electronics">Electronics</option>
                <option value="Clothing">Clothing</option>
                <option value="Home & Garden">Home & Garden</option>
                <option value="Sports & Outdoors">Sports & Outdoors</option>
                <option value="Books">Books</option>
                <option value="Health & Beauty">Health & Beauty</option>
                <option value="Toys & Games">Toys & Games</option>
                <option value="Automotive">Automotive</option>
                <option value="Food & Beverages">Food & Beverages</option>
                <option value="Jewelry & Accessories">Jewelry & Accessories</option>
              </optgroup>
              {state.categories.length > 0 && (
                <optgroup label="Other Categories">
                  {state.categories
                    .filter(cat => !['Electronics', 'Clothing', 'Home & Garden', 'Sports & Outdoors', 'Books', 'Health & Beauty', 'Toys & Games', 'Automotive', 'Food & Beverages', 'Jewelry & Accessories'].includes(cat))
                    .map((category) => (
                      <option key={category} value={category}>
                        {category}
                      </option>
                    ))}
                </optgroup>
              )}
            </select>
          </div>
        </div>
      </div>

      {/* Products Table */}
      <div className="bg-gradient-to-br from-white to-green-50/30 rounded-2xl shadow-xl overflow-hidden border border-green-100">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-green-200">
            <thead className="bg-gradient-to-r from-green-600 to-emerald-600">
              <tr>
                <th className="px-6 py-4 text-left text-xs font-bold text-white uppercase tracking-wider">
                  Product
                </th>
                <th className="px-6 py-4 text-left text-xs font-bold text-white uppercase tracking-wider">
                  Category
                </th>
                <th className="px-6 py-4 text-left text-xs font-bold text-white uppercase tracking-wider">
                  Price
                </th>
                <th className="px-6 py-4 text-left text-xs font-bold text-white uppercase tracking-wider">
                  Stock
                </th>
                <th className="px-6 py-4 text-left text-xs font-bold text-white uppercase tracking-wider">
                  Featured
                </th>
                <th className="px-6 py-4 text-right text-xs font-bold text-white uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-green-100">
              {state.loading ? (
                <tr>
                  <td colSpan={6} className="px-6 py-4 text-center">
                    <div className="flex justify-center">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900"></div>
                    </div>
                  </td>
                </tr>
              ) : state.products.length === 0 ? (
                <tr>
                  <td colSpan={6} className="px-6 py-4 text-center text-gray-500">
                    No products found
                  </td>
                </tr>
              ) : (
                state.products.map((product) => (
                  <tr key={product._id} className="hover:bg-green-50/50 transition-colors duration-200">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="h-10 w-10 flex-shrink-0">
                          {product.image ? (
                            <img
                              className="h-10 w-10 rounded-md object-cover"
                              src={product.image.startsWith('http') || product.image.startsWith('/uploads/') ? product.image : `${API_URL}${product.image}`}
                              alt={product.name}
                              onError={(e) => {
                                // If image fails to load, replace with a data URI placeholder
                                (e.target as HTMLImageElement).src = 'data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%2240%22%20height%3D%2240%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%2040%2040%22%20preserveAspectRatio%3D%22none%22%3E%3Cdefs%3E%3Cstyle%20type%3D%22text%2Fcss%22%3E%23holder_189077289b1%20text%20%7B%20fill%3A%23AAAAAA%3Bfont-weight%3Abold%3Bfont-family%3AArial%2C%20Helvetica%2C%20Open%20Sans%2C%20sans-serif%2C%20monospace%3Bfont-size%3A6pt%20%7D%20%3C%2Fstyle%3E%3C%2Fdefs%3E%3Cg%20id%3D%22holder_189077289b1%22%3E%3Crect%20width%3D%2240%22%20height%3D%2240%22%20fill%3D%22%23EEEEEE%22%3E%3C%2Frect%3E%3Cg%3E%3Ctext%20x%3D%2210%22%20y%3D%2220%22%3ENo%20img%3C%2Ftext%3E%3C%2Fg%3E%3C%2Fg%3E%3C%2Fsvg%3E';
                              }}
                            />
                          ) : (
                            <div className="h-10 w-10 rounded-md bg-gray-200 flex items-center justify-center">
                              <span className="text-gray-400 text-xs">No img</span>
                            </div>
                          )}
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">{product.name}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-500">{product.category}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{formatCurrency(product.price)}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span
                        className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          !product.inStock
                            ? 'bg-red-100 text-red-800'
                            : 'bg-green-100 text-green-800'
                        }`}
                      >
                        {product.inStock ? (
                          <>
                            In stock
                            {product.quantity !== undefined && (
                              <span className="ml-1">({product.quantity})</span>
                            )}
                          </>
                        ) : (
                          'Out of stock'
                        )}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span
                        className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          product.featured ? 'bg-indigo-100 text-indigo-800' : 'bg-gray-100 text-gray-800'
                        }`}
                      >
                        {product.featured ? 'Yes' : 'No'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex justify-end space-x-2">
                        <button
                          onClick={() => handleEditProduct(product)}
                          className="p-2 text-green-600 hover:text-white hover:bg-green-600 rounded-lg transition-all duration-200 hover:scale-110"
                          title="Edit Product"
                        >
                          <Edit className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => setDeleteConfirm(product._id)}
                          className="p-2 text-red-600 hover:text-white hover:bg-red-600 rounded-lg transition-all duration-200 hover:scale-110"
                          title="Delete Product"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {state.totalPages > 1 && (
          <div className="px-6 py-3 flex items-center justify-between border-t border-gray-200">
            <div className="flex-1 flex justify-between sm:hidden">
              <button
                onClick={() => handlePageChange(Math.max(1, state.currentPage - 1))}
                disabled={state.currentPage === 1}
                className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Previous
              </button>
              <button
                onClick={() => handlePageChange(Math.min(state.totalPages, state.currentPage + 1))}
                disabled={state.currentPage === state.totalPages}
                className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Next
              </button>
            </div>
            <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p className="text-sm text-gray-700">
                  Showing page <span className="font-medium">{state.currentPage}</span> of{' '}
                  <span className="font-medium">{state.totalPages}</span>
                </p>
              </div>
              <div>
                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                  <button
                    onClick={() => handlePageChange(Math.max(1, state.currentPage - 1))}
                    disabled={state.currentPage === 1}
                    className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <span className="sr-only">Previous</span>
                    &lsaquo;
                  </button>
                  {Array.from({ length: state.totalPages }, (_, i) => i + 1).map((page) => (
                    <button
                      key={page}
                      onClick={() => handlePageChange(page)}
                      className={`relative inline-flex items-center px-4 py-2 border ${state.currentPage === page ? 'bg-indigo-50 border-indigo-500 text-indigo-600 z-10' : 'border-gray-300 bg-white text-gray-500 hover:bg-gray-50'} text-sm font-medium`}
                    >
                      {page}
                    </button>
                  ))}
                  <button
                    onClick={() => handlePageChange(Math.min(state.totalPages, state.currentPage + 1))}
                    disabled={state.currentPage === state.totalPages}
                    className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <span className="sr-only">Next</span>
                    &rsaquo;
                  </button>
                </nav>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Product Form Modal */}
      {showForm && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-black opacity-60 backdrop-blur-sm"></div>
            </div>

            <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">
              &#8203;
            </span>

            <div className="inline-block align-bottom bg-white rounded-2xl text-left overflow-hidden shadow-2xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full border border-gray-200">
              <form onSubmit={handleSubmit}>
                <div className="bg-gradient-to-r from-green-600 to-emerald-600 px-6 py-4">
                  <div className="flex items-center">
                    <div className="w-8 h-8 bg-white rounded-full flex items-center justify-center mr-3">
                      <Package className="h-5 w-5 text-green-600" />
                    </div>
                    <h3 className="text-xl font-bold text-white">
                      {isEditing ? 'Edit Product' : 'Add New Product'}
                    </h3>
                  </div>
                </div>
                <div className="px-8 py-8 bg-gray-50/50">

                  <div className="mb-6">
                    <label htmlFor="name" className="block text-sm font-semibold text-gray-800 mb-2">
                      Product Name
                    </label>
                    <input
                      type="text"
                      id="name"
                      className={`w-full px-4 py-3 border-2 ${formErrors.name ? 'border-red-500 bg-red-50' : 'border-gray-300'} rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200 bg-white hover:border-green-400 shadow-sm`}
                      value={formData.name}
                      onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                      placeholder="Enter product name"
                    />
                    {formErrors.name && <p className="mt-2 text-sm text-red-600 font-medium">{formErrors.name}</p>}
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                      <label htmlFor="price" className="block text-sm font-semibold text-gray-800 mb-2">
                        Price ($)
                      </label>
                      <input
                        type="number"
                        id="price"
                        min="0"
                        step="0.01"
                        className={`w-full px-4 py-3 border-2 ${formErrors.price ? 'border-red-500 bg-red-50' : 'border-gray-300'} rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200 bg-white hover:border-green-400 shadow-sm`}
                        value={formData.price}
                        onChange={(e) => setFormData({ ...formData, price: parseFloat(e.target.value) || 0 })}
                        placeholder="0.00"
                      />
                      {formErrors.price && <p className="mt-2 text-sm text-red-600 font-medium">{formErrors.price}</p>}
                    </div>

                    {/* Show calculated original price when discount is applied */}
                    {isEditing && formData.discountValue > 0 && formData.discountType === 'percentage' && (
                      <div>
                        <label className="block text-sm font-semibold text-gray-800 mb-2">
                          Calculated Original Price ($)
                        </label>
                        <div className="w-full px-4 py-3 border-2 border-green-300 bg-green-50 rounded-xl text-gray-700 font-semibold">
                          ${((formData.price || 0) / (1 - (formData.discountValue || 0) / 100)).toFixed(2)}
                        </div>
                        <p className="text-xs text-green-600 mt-1">
                          ✨ Auto-calculated: Current price ÷ (1 - {formData.discountValue}% discount)
                        </p>
                      </div>
                    )}
                  </div>

                  {/* Discount Section */}
                  <div className={`mb-6 p-4 rounded-xl border ${isEditing ? 'bg-blue-50 border-blue-200' : 'bg-gray-100 border-gray-300'}`}>
                    <h4 className={`text-lg font-semibold mb-4 flex items-center ${isEditing ? 'text-blue-800' : 'text-gray-500'}`}>
                      <span className={`w-6 h-6 rounded-full flex items-center justify-center mr-2 ${isEditing ? 'bg-blue-600' : 'bg-gray-400'}`}>
                        <span className="text-white text-xs">%</span>
                      </span>
                      Product Discount {!isEditing && '(Available Only for Existing Products)'}
                      <a
                        href="/admin/discounts"
                        target="_blank"
                        className={`ml-auto text-xs px-3 py-1 rounded-full transition-colors ${isEditing ? 'bg-blue-600 text-white hover:bg-blue-700' : 'bg-gray-400 text-white cursor-not-allowed'}`}
                      >
                        🎯 Manage Store Discounts
                      </a>
                    </h4>

                    {!isEditing && (
                      <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                        <div className="flex items-center">
                          <span className="text-yellow-600 mr-2">⚠️</span>
                          <div>
                            <p className="text-sm font-medium text-yellow-800">Discounts Not Available for New Products</p>
                            <p className="text-xs text-yellow-600">Save the product first, then edit it to add discounts. This ensures proper price calculations.</p>
                          </div>
                        </div>
                      </div>
                    )}



                    {/* Auto-calculated Discount Display */}
                    {formData.price > 0 && formData.originalPrice > 0 && formData.originalPrice > formData.price && (
                      <div className="mb-4 p-4 bg-green-50 border border-green-200 rounded-lg">
                        <div className="flex items-center justify-between">
                          <div>
                            <span className="text-sm font-medium text-green-800">✨ Auto-calculated Discount:</span>
                            <span className="ml-2 text-xl font-bold text-green-600">
                              {Math.round(((formData.originalPrice - formData.price) / formData.originalPrice) * 100)}% OFF
                            </span>
                          </div>
                          <div className="text-right">
                            <div className="text-sm text-gray-600">Customer Saves:</div>
                            <div className="text-xl font-bold text-green-600">
                              ${(formData.originalPrice - formData.price).toFixed(2)}
                            </div>
                          </div>
                        </div>
                        <p className="text-xs text-green-600 mt-2">
                          💡 Discount percentage is automatically calculated from Original Price - Current Price
                        </p>
                      </div>
                    )}

                    {isEditing && (
                      <div className="grid grid-cols-1 gap-4 mb-4">
                        <div>
                          <label htmlFor="discountCode" className="block text-sm font-semibold text-gray-800 mb-2">
                            🎯 Select Discount Template (Optional)
                          </label>
                          <select
                            id="discountCode"
                            className="w-full px-4 py-3 border-2 border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-white hover:border-blue-400 shadow-sm"
                            value={formData.selectedDiscountTemplate || ''}
                            onChange={(e) => {
                              const selectedDiscount = availableDiscounts.find(discount => discount._id === e.target.value);
                              if (selectedDiscount) {
                                // Calculate original price based on current price and discount percentage
                                let calculatedOriginalPrice = formData.price;
                                if (selectedDiscount.type === 'percentage' && formData.price > 0) {
                                  calculatedOriginalPrice = formData.price / (1 - selectedDiscount.value / 100);
                                }

                                setFormData({
                                  ...formData,
                                  selectedDiscountTemplate: selectedDiscount._id,
                                  discountType: selectedDiscount.type,
                                  discountValue: selectedDiscount.value,
                                  originalPrice: calculatedOriginalPrice,
                                  discountStartDate: new Date().toISOString().split('T')[0],
                                  discountEndDate: selectedDiscount.validUntil ? new Date(selectedDiscount.validUntil).toISOString().split('T')[0] : ''
                                });
                                console.log(`Applied discount: ${selectedDiscount.name} (${selectedDiscount.value}${selectedDiscount.type === 'percentage' ? '%' : '$'} off)`);
                              } else {
                                setFormData({
                                  ...formData,
                                  selectedDiscountTemplate: '',
                                  discountType: 'none',
                                  discountValue: 0,
                                  originalPrice: formData.price,
                                  discountStartDate: '',
                                  discountEndDate: ''
                                });
                              }
                            }}
                          >
                          <option value="">No Product Discount</option>
                          <optgroup label="Available Discount Templates (Active Only)">
                            {availableDiscounts
                              .filter(discount => discount.isActive === true)
                              .map((discount) => (
                                <option key={discount._id} value={discount._id}>
                                  {discount.name} - {discount.value}{discount.type === 'percentage' ? '%' : '$'} off
                                  ({discount.code})
                                </option>
                              ))}
                          </optgroup>
                        </select>
                        <p className="text-xs text-gray-500 mt-1">
                          💡 Select a discount template - Original price will be calculated automatically
                        </p>
                      </div>
                    </div>
                    )}

                    {/* Show applied discount details */}
                    {isEditing && formData.discountType !== 'none' && formData.discountValue > 0 && (
                      <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                        <div className="flex items-center justify-between">
                          <div>
                            <span className="text-sm font-medium text-blue-800">Applied Discount:</span>
                            <span className="ml-2 text-lg font-bold text-blue-600">
                              {formData.discountValue}{formData.discountType === 'percentage' ? '%' : '$'} OFF
                            </span>
                          </div>
                          <button
                            type="button"
                            onClick={() => setFormData({
                              ...formData,
                              selectedDiscountTemplate: '',
                              discountType: 'none',
                              discountValue: 0,
                              originalPrice: formData.price,
                              discountStartDate: '',
                              discountEndDate: ''
                            })}
                            className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                          >
                            Remove Discount
                          </button>
                        </div>
                        {formData.discountType === 'percentage' && formData.price > 0 && (
                          <div className="mt-2 text-xs text-blue-600">
                            💡 Original Price: ${((formData.price || 0) / (1 - (formData.discountValue || 0) / 100)).toFixed(2)}
                            → Current Price: ${formData.price.toFixed(2)}
                            (Customer saves: ${(((formData.price || 0) / (1 - (formData.discountValue || 0) / 100)) - formData.price).toFixed(2)})
                          </div>
                        )}
                      </div>
                    )}

                    {formData.discountType !== 'none' && (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label htmlFor="discountStartDate" className="block text-sm font-semibold text-gray-800 mb-2">
                            Start Date
                          </label>
                          <input
                            type="date"
                            id="discountStartDate"
                            className="w-full px-4 py-3 border-2 border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200 bg-white hover:border-green-400 shadow-sm"
                            value={formData.discountStartDate || ''}
                            onChange={(e) => setFormData({ ...formData, discountStartDate: e.target.value })}
                          />
                        </div>

                        <div>
                          <label htmlFor="discountEndDate" className="block text-sm font-semibold text-gray-800 mb-2">
                            End Date
                          </label>
                          <input
                            type="date"
                            id="discountEndDate"
                            className="w-full px-4 py-3 border-2 border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200 bg-white hover:border-green-400 shadow-sm"
                            value={formData.discountEndDate || ''}
                            onChange={(e) => setFormData({ ...formData, discountEndDate: e.target.value })}
                          />
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Tax Section */}
                  <div className="mb-6 p-4 bg-green-50 rounded-xl border border-green-200">
                    <h4 className="text-lg font-semibold text-green-800 mb-4 flex items-center">
                      <span className="w-6 h-6 bg-green-600 rounded-full flex items-center justify-center mr-2">
                        <span className="text-white text-xs">$</span>
                      </span>
                      Tax Settings
                      <a
                        href="/admin/taxes"
                        target="_blank"
                        className="ml-auto text-xs bg-green-600 text-white px-3 py-1 rounded-full hover:bg-green-700 transition-colors"
                      >
                        📋 Manage Tax Rules
                      </a>
                    </h4>



                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label htmlFor="taxRule" className="block text-sm font-semibold text-gray-800 mb-2">
                          🏷️ Select Tax Rule
                        </label>
                        <select
                          id="taxRule"
                          className="w-full px-4 py-3 border-2 border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200 bg-white hover:border-green-400 shadow-sm"
                          value={formData.selectedTaxRule || ''}
                          onChange={(e) => {
                            const selectedRule = availableTaxRules.find(rule => rule._id === e.target.value);
                            if (selectedRule) {
                              setFormData({
                                ...formData,
                                selectedTaxRule: selectedRule._id,
                                taxRate: selectedRule.rate,
                                taxIncluded: false
                              });
                              alert(`Applied tax rule: ${selectedRule.name} (${selectedRule.rate}%)`);
                            } else {
                              setFormData({
                                ...formData,
                                selectedTaxRule: '',
                                taxRate: 0,
                                taxIncluded: false
                              });
                            }
                          }}
                        >
                          <option value="">No Tax Applied</option>
                          {availableTaxRules
                            .filter(rule => rule.isActive === true)
                            .map((rule) => (
                              <option key={rule._id} value={rule._id}>
                                {rule.name} - {rule.rate}% ({rule.description})
                              </option>
                            ))}
                        </select>
                        <p className="text-xs text-gray-500 mt-1">
                          💡 Select from existing tax rules managed in Tax Rules section
                        </p>
                      </div>

                      <div>
                        <label className="block text-sm font-semibold text-gray-800 mb-2">
                          Tax Included in Price
                        </label>
                        <div className="flex items-center bg-white border-2 border-gray-300 rounded-xl px-4 py-3 shadow-sm hover:border-green-400 transition-all duration-200">
                          <input
                            id="taxIncluded"
                            type="checkbox"
                            className="h-5 w-5 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                            checked={formData.taxIncluded || false}
                            onChange={(e) => setFormData({ ...formData, taxIncluded: e.target.checked })}
                          />
                          <label htmlFor="taxIncluded" className="ml-3 block text-sm font-medium text-gray-900">
                            Tax is included in the displayed price
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                      <label htmlFor="quantity" className="block text-sm font-semibold text-gray-800 mb-2">
                        Stock Quantity
                      </label>
                      <input
                        type="number"
                        id="quantity"
                        min="0"
                        step="1"
                        className={`w-full px-4 py-3 border-2 ${formErrors.quantity ? 'border-red-500 bg-red-50' : 'border-gray-300'} rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200 bg-white hover:border-green-400 shadow-sm`}
                        value={formData.quantity || 0}
                        onChange={(e) => setFormData({ ...formData, quantity: parseInt(e.target.value) || 0, inStock: parseInt(e.target.value) > 0 })}
                        placeholder="0"
                      />
                      {formErrors.quantity && <p className="mt-2 text-sm text-red-600 font-medium">{formErrors.quantity}</p>}
                    </div>

                    <div>
                      <label className="block text-sm font-semibold text-gray-800 mb-2">
                        Stock Status
                      </label>
                      <div className="flex items-center bg-white border-2 border-gray-300 rounded-xl px-4 py-3 shadow-sm hover:border-green-400 transition-all duration-200">
                        <input
                          id="inStock"
                          type="checkbox"
                          className="h-5 w-5 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                          checked={formData.inStock}
                          onChange={(e) => setFormData({ ...formData, inStock: e.target.checked, quantity: e.target.checked ? (formData.quantity || 1) : 0 })}
                        />
                        <label htmlFor="inStock" className="ml-3 block text-sm font-medium text-gray-900">
                          In Stock
                        </label>
                      </div>
                      {formErrors.inStock && <p className="mt-2 text-sm text-red-600 font-medium">{formErrors.inStock}</p>}
                    </div>
                  </div>

                  <div className="mb-6">
                    <label htmlFor="category" className="block text-sm font-semibold text-gray-800 mb-2">
                      Category
                    </label>
                    <select
                      id="category"
                      className={`w-full px-4 py-3 border-2 ${formErrors.category ? 'border-red-500 bg-red-50' : 'border-gray-300'} rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200 bg-white hover:border-green-400 shadow-sm`}
                      value={formData.category}
                      onChange={(e) => {
                        const newCategory = e.target.value;
                        setFormData({ ...formData, category: newCategory });
                        // Auto-apply tax rule when category changes
                        if (newCategory && availableTaxRules.length > 0) {
                          autoApplyTaxRule(newCategory);
                        }
                      }}
                    >
                      <option value="">Select a category</option>
                      <optgroup label="Popular Categories">
                        <option value="Electronics">Electronics</option>
                        <option value="Clothing">Clothing</option>
                        <option value="Home & Garden">Home & Garden</option>
                        <option value="Sports & Outdoors">Sports & Outdoors</option>
                        <option value="Books">Books</option>
                        <option value="Health & Beauty">Health & Beauty</option>
                        <option value="Toys & Games">Toys & Games</option>
                        <option value="Automotive">Automotive</option>
                        <option value="Food & Beverages">Food & Beverages</option>
                        <option value="Jewelry & Accessories">Jewelry & Accessories</option>
                      </optgroup>
                      {state.categories.length > 0 && (
                        <optgroup label="Existing Categories">
                          {state.categories
                            .filter(cat => !['Electronics', 'Clothing', 'Home & Garden', 'Sports & Outdoors', 'Books', 'Health & Beauty', 'Toys & Games', 'Automotive', 'Food & Beverages', 'Jewelry & Accessories'].includes(cat))
                            .map((category) => (
                              <option key={category} value={category}>
                                {category}
                              </option>
                            ))}
                        </optgroup>
                      )}
                    </select>
                    {formErrors.category && <p className="mt-2 text-sm text-red-600 font-medium">{formErrors.category}</p>}
                    <p className="mt-2 text-sm text-gray-600 bg-blue-50 p-3 rounded-lg border border-blue-200">
                      💡 Select from popular categories or choose from existing ones. If you need a new category, contact the system administrator.
                    </p>
                  </div>

                  <div className="mb-6">
                    <label htmlFor="brand" className="block text-sm font-semibold text-gray-800 mb-2">
                      Brand
                    </label>
                    <input
                      type="text"
                      id="brand"
                      className={`w-full px-4 py-3 border-2 ${formErrors.brand ? 'border-red-500 bg-red-50' : 'border-gray-300'} rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200 bg-white hover:border-green-400 shadow-sm`}
                      value={formData.brand}
                      onChange={(e) => setFormData({ ...formData, brand: e.target.value })}
                      placeholder="Enter product brand"
                    />
                    {formErrors.brand && <p className="mt-2 text-sm text-red-600 font-medium">{formErrors.brand}</p>}
                  </div>

                  <div className="mb-6">
                    <label htmlFor="description" className="block text-sm font-semibold text-gray-800 mb-2">
                      Description
                    </label>
                    <textarea
                      id="description"
                      rows={4}
                      className={`w-full px-4 py-3 border-2 ${formErrors.description ? 'border-red-500 bg-red-50' : 'border-gray-300'} rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200 bg-white hover:border-green-400 shadow-sm resize-none`}
                      value={formData.description}
                      onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                      placeholder="Enter a detailed description of the product..."
                    />
                    {formErrors.description && <p className="mt-2 text-sm text-red-600 font-medium">{formErrors.description}</p>}
                  </div>

                  <div className="mb-6">
                    <label className="block text-sm font-semibold text-gray-800 mb-2">
                      Product Settings
                    </label>
                    <div className="flex items-center bg-white border-2 border-gray-300 rounded-xl px-4 py-3 shadow-sm hover:border-green-400 transition-all duration-200">
                      <input
                        id="featured"
                        type="checkbox"
                        className="h-5 w-5 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                        checked={formData.featured}
                        onChange={(e) => setFormData({ ...formData, featured: e.target.checked })}
                      />
                      <label htmlFor="featured" className="ml-3 block text-sm font-medium text-gray-900">
                        ⭐ Featured Product
                      </label>
                    </div>
                    <p className="mt-2 text-sm text-gray-600">Featured products appear prominently on the homepage</p>
                  </div>

                  <div className="mb-6">
                    <label className="block text-sm font-semibold text-gray-800 mb-2">
                      Main Product Image
                    </label>
                    <div className="bg-white border-2 border-gray-300 rounded-xl p-4 hover:border-green-400 transition-all duration-200 shadow-sm">
                      <div className="flex items-center space-x-4">
                        {formData.imagePreview ? (
                          <div className="relative">
                            <img
                              src={formData.imagePreview}
                              alt="Product preview"
                              className="h-24 w-24 object-cover rounded-xl border-2 border-green-200 shadow-md"
                              onError={(e) => {
                                // If image fails to load, replace with a data URI placeholder
                                (e.target as HTMLImageElement).src = 'data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%2280%22%20height%3D%2280%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%2080%2080%22%20preserveAspectRatio%3D%22none%22%3E%3Cdefs%3E%3Cstyle%20type%3D%22text%2Fcss%22%3E%23holder_189077289b1%20text%20%7B%20fill%3A%23AAAAAA%3Bfont-weight%3Abold%3Bfont-family%3AArial%2C%20Helvetica%2C%20Open%20Sans%2C%20sans-serif%2C%20monospace%3Bfont-size%3A10pt%20%7D%20%3C%2Fstyle%3E%3C%2Fdefs%3E%3Cg%20id%3D%22holder_189077289b1%22%3E%3Crect%20width%3D%2280%22%20height%3D%2280%22%20fill%3D%22%23EEEEEE%22%3E%3C%2Frect%3E%3Cg%3E%3Ctext%20x%3D%2220%22%20y%3D%2245%22%3EPreview%3C%2Ftext%3E%3C%2Fg%3E%3C%2Fg%3E%3C%2Fsvg%3E';
                              }}
                            />
                            <div className="absolute -top-2 -right-2 bg-green-500 text-white rounded-full p-1">
                              <ImageIcon className="h-3 w-3" />
                            </div>
                          </div>
                        ) : (
                          <div className="h-24 w-24 border-2 border-dashed border-gray-400 rounded-xl flex items-center justify-center bg-gray-50">
                            <ImageIcon className="h-10 w-10 text-gray-400" />
                          </div>
                        )}
                      <div>
                        <label
                          htmlFor="image-upload"
                          className="cursor-pointer bg-white py-2 px-3 border border-gray-300 rounded-md shadow-sm text-sm leading-4 font-medium text-gray-700 hover:bg-gray-50 focus:outline-none"
                        >
                          {formData.imagePreview ? 'Change Image' : 'Upload Image'}
                        </label>
                        <input
                          id="image-upload"
                          name="image"
                          type="file"
                          accept="image/*"
                          className="sr-only"
                          onChange={handleImageChange}
                        />
                      </div>
                    </div>
                    {formErrors.image && <p className="mt-1 text-sm text-red-500">{formErrors.image}</p>}
                  </div>

                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Additional Images
                    </label>
                    <div className="grid grid-cols-4 gap-2 mb-2">
                      {formData.additionalImagePreviews.map((preview, index) => (
                        <div key={index} className="relative">
                          <img
                            src={preview}
                            alt={`Additional ${index + 1}`}
                            className="h-16 w-16 object-cover rounded-md"
                            onError={(e) => {
                              // If image fails to load, replace with a data URI placeholder
                              (e.target as HTMLImageElement).src = 'data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%2264%22%20height%3D%2264%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%2064%2064%22%20preserveAspectRatio%3D%22none%22%3E%3Cdefs%3E%3Cstyle%20type%3D%22text%2Fcss%22%3E%23holder_189077289b1%20text%20%7B%20fill%3A%23AAAAAA%3Bfont-weight%3Abold%3Bfont-family%3AArial%2C%20Helvetica%2C%20Open%20Sans%2C%20sans-serif%2C%20monospace%3Bfont-size%3A8pt%20%7D%20%3C%2Fstyle%3E%3C%2Fdefs%3E%3Cg%20id%3D%22holder_189077289b1%22%3E%3Crect%20width%3D%2264%22%20height%3D%2264%22%20fill%3D%22%23EEEEEE%22%3E%3C%2Frect%3E%3Cg%3E%3Ctext%20x%3D%2215%22%20y%3D%2236%22%3EPreview%3C%2Ftext%3E%3C%2Fg%3E%3C%2Fg%3E%3C%2Fsvg%3E';
                            }}
                          />
                          <button
                            type="button"
                            onClick={() => removeAdditionalImage(index)}
                            className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 shadow-sm hover:bg-red-600 focus:outline-none"
                          >
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              className="h-3 w-3"
                              viewBox="0 0 20 20"
                              fill="currentColor"
                            >
                              <path
                                fillRule="evenodd"
                                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                                clipRule="evenodd"
                              />
                            </svg>
                          </button>
                        </div>
                      ))}
                      <div className="h-16 w-16 border-2 border-dashed border-gray-300 rounded-md flex items-center justify-center">
                        <label
                          htmlFor="additional-images-upload"
                          className="cursor-pointer flex flex-col items-center justify-center w-full h-full"
                        >
                          <Plus className="h-6 w-6 text-gray-400" />
                          <span className="text-xs text-gray-500">Add</span>
                          <input
                            id="additional-images-upload"
                            name="additionalImages"
                            type="file"
                            accept="image/*"
                            multiple
                            className="sr-only"
                            onChange={handleAdditionalImagesChange}
                          />
                        </label>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Real-time Pricing Preview */}
                {(formData.price > 0 || formData.originalPrice > 0 || formData.taxRate > 0) && (
                  <div className="px-6 py-4 bg-gradient-to-r from-purple-50 to-pink-50 border-t border-purple-200">
                    <h4 className="text-lg font-semibold text-purple-800 mb-4 flex items-center">
                      <span className="w-6 h-6 bg-purple-600 rounded-full flex items-center justify-center mr-2">
                        <span className="text-white text-xs">💰</span>
                      </span>
                      Customer Pricing Preview
                    </h4>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="bg-white p-4 rounded-lg border border-purple-300">
                        <h5 className="font-semibold text-purple-800 mb-2">Product Card Display</h5>
                        <div className="space-y-2">
                          <div className="flex items-center space-x-2">
                            <span className="text-lg font-bold text-gray-900">
                              ${formData.price.toFixed(2)}
                            </span>
                            {formData.originalPrice > 0 && formData.originalPrice > formData.price && (
                              <span className="text-sm text-gray-500 line-through">
                                ${formData.originalPrice.toFixed(2)}
                              </span>
                            )}
                          </div>
                          {formData.originalPrice > 0 && formData.originalPrice > formData.price && (
                            <div className="text-xs text-green-600 font-semibold">
                              💰 Save ${(formData.originalPrice - formData.price).toFixed(2)}
                              ({Math.round(((formData.originalPrice - formData.price) / formData.originalPrice) * 100)}% OFF)
                            </div>
                          )}
                          {formData.taxRate > 0 && (
                            <div className="text-xs text-blue-600">
                              {formData.taxIncluded ? 'Tax included' : `+${formData.taxRate}% tax`}
                            </div>
                          )}
                        </div>
                      </div>

                      <div className="bg-white p-4 rounded-lg border border-purple-300">
                        <h5 className="font-semibold text-purple-800 mb-2">Checkout Calculation</h5>
                        <div className="space-y-1 text-sm">
                          <div className="flex justify-between">
                            <span>Product Price:</span>
                            <span>${formData.price.toFixed(2)}</span>
                          </div>
                          {formData.taxRate > 0 && !formData.taxIncluded && (
                            <div className="flex justify-between text-blue-600">
                              <span>Tax ({formData.taxRate}%):</span>
                              <span>+${((formData.price * formData.taxRate) / 100).toFixed(2)}</span>
                            </div>
                          )}
                          <div className="border-t pt-1 flex justify-between font-bold">
                            <span>Customer Pays:</span>
                            <span>
                              ${formData.taxIncluded
                                ? formData.price.toFixed(2)
                                : (formData.price * (1 + (formData.taxRate || 0) / 100)).toFixed(2)
                              }
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
                </div>

                <div className="bg-gradient-to-r from-green-50 to-emerald-50 px-6 py-4 sm:flex sm:flex-row-reverse sm:space-x-reverse sm:space-x-3">
                  <button
                    type="submit"
                    className="w-full inline-flex justify-center rounded-xl border border-transparent shadow-lg px-6 py-3 bg-gradient-to-r from-green-600 to-emerald-600 text-base font-semibold text-white hover:from-green-700 hover:to-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 sm:w-auto transform hover:scale-105 transition-all duration-200"
                  >
                    {isEditing ? 'Update Product' : 'Add Product'}
                  </button>
                  <button
                    type="button"
                    className="mt-3 w-full inline-flex justify-center rounded-xl border-2 border-gray-300 shadow-sm px-6 py-3 bg-white text-base font-semibold text-gray-700 hover:bg-gray-50 hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 sm:mt-0 sm:w-auto transition-all duration-200"
                    onClick={() => setShowForm(false)}
                  >
                    Cancel
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {deleteConfirm && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">
              &#8203;
            </span>

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="sm:flex sm:items-start">
                  <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                    <AlertCircle className="h-6 w-6 text-red-600" />
                  </div>
                  <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                    <h3 className="text-lg leading-6 font-medium text-gray-900">Delete Product</h3>
                    <div className="mt-2">
                      <p className="text-sm text-gray-500">
                        Are you sure you want to delete this product? This action cannot be undone.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  type="button"
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm"
                  onClick={() => handleDeleteProduct(deleteConfirm)}
                >
                  Delete
                </button>
                <button
                  type="button"
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                  onClick={() => setDeleteConfirm(null)}
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </AdminLayout>
  );
};

export default Products;