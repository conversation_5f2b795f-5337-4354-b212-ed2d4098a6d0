import { useState, useEffect } from 'react';
import { Link, useNavigate, useSearchParams } from 'react-router-dom';
import { User, CreditCard, ShoppingBag, Settings, LogOut, AlertCircle, ExternalLink, Save, Info, LayoutDashboard } from 'lucide-react';
import { PaymentVerificationDetails } from '../components/PaymentVerificationDetails';
import { ConfirmDialog } from '../components/ConfirmDialog';
import { useAuth } from '../context/AuthContext';
import { API_URL } from '../utils/env';

interface Order {
  _id: string;
  orderNumber: string;
  user: string;
  items: Array<{
    product: {
      _id: string;
      name: string;
      price: number;
      image: string;
    };
    quantity: number;
    price: number;
  }>;
  totalAmount: number;
  shippingAddress: {
    address: string;
    city: string;
    postalCode: string;
    country: string;
  };
  paymentStatus: string;
  paymentId?: string;
  status?: string; // Add status field
  orderStatus: string;
  createdAt: string;
  total?: number; // Add total field
}

export function Account() {
  const [searchParams] = useSearchParams();
  const [activeTab, setActiveTab] = useState(searchParams.get('tab') || 'profile');
  const { user, token, logout } = useAuth();
  const navigate = useNavigate();

  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showLogoutConfirm, setShowLogoutConfirm] = useState(false);
  
  useEffect(() => {
    if (!user) {
      navigate('/login');
      return;
    }
    
    if (activeTab === 'orders') {
      fetchOrders();
    }
  }, [user, activeTab, token, navigate]);
  
  const fetchOrders = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch(`${API_URL}/api/orders/myorders`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      
      // Get the response text first
      const responseText = await response.text();
      
      if (!response.ok) {
        throw new Error('Failed to fetch orders');
      }
      
      // Parse the response text as JSON
      let data;
      try {
        data = JSON.parse(responseText);
        console.log('Orders data received:', data);

        // Handle different response formats
        if (Array.isArray(data)) {
          setOrders(data);
        } else if (data.orders && Array.isArray(data.orders)) {
          setOrders(data.orders);
        } else if (data.data && Array.isArray(data.data)) {
          setOrders(data.data);
        } else {
          console.warn('Unexpected orders response format:', data);
          setOrders([]);
        }
      } catch (parseError) {
        console.error('Error parsing orders response:', parseError);
        console.error('Response text:', responseText);
        throw new Error('Failed to parse orders response');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };
  
  const handleLogout = () => {
    setShowLogoutConfirm(true);
  };

  const confirmLogout = () => {
    logout();
    navigate('/');
    setShowLogoutConfirm(false);
  };

  return (
    <div className="container mx-auto px-4 py-12 max-w-6xl">
      <h1 className="text-3xl font-bold text-brand-dark mb-8 text-center md:text-left">My Account</h1>
      <div className="flex flex-col md:flex-row gap-8">
        {/* Sidebar */}
        <div className="w-full md:w-1/4">
          <div className="bg-white rounded-xl shadow-lg border border-brand-light p-6 sticky top-24">
            <div className="flex items-center space-x-4 mb-8">
              <div className="w-16 h-16 rounded-full overflow-hidden bg-brand-light flex items-center justify-center border-2 border-brand-primary shadow-md">
                {/* Always show the User icon since we don't have avatar in our User interface */}
                <User size={32} className="text-brand-primary" />
              </div>
              <div>
                <h2 className="text-xl font-bold text-brand-dark">{user ? `${user.firstName} ${user.lastName}` : 'Loading...'}</h2>
                <p className="text-brand-secondary text-sm">{user?.email}</p>
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-brand-light text-brand-primary mt-2">
                  {user?.role === 'admin' ? 'Administrator' : 'Customer'}
                </span>
              </div>
            </div>
            
            <div className="space-y-3 mt-2">
              <button 
                onClick={() => setActiveTab('profile')}
                className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg transition-all duration-200 ${activeTab === 'profile' ? 'bg-brand-primary text-white shadow-md' : 'hover:bg-brand-light text-brand-secondary hover:text-brand-primary'}`}
              >
                <User size={18} className={activeTab === 'profile' ? 'text-white' : ''} />
                <span className="font-medium">Profile</span>
              </button>
              
              <button 
                onClick={() => setActiveTab('payments')}
                className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg transition-all duration-200 ${activeTab === 'payments' ? 'bg-brand-primary text-white shadow-md' : 'hover:bg-brand-light text-brand-secondary hover:text-brand-primary'}`}
              >
                <CreditCard size={18} className={activeTab === 'payments' ? 'text-white' : ''} />
                <span className="font-medium">Payment History</span>
              </button>
              
              <button 
                onClick={() => setActiveTab('orders')}
                className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg transition-all duration-200 ${activeTab === 'orders' ? 'bg-brand-primary text-white shadow-md' : 'hover:bg-brand-light text-brand-secondary hover:text-brand-primary'}`}
              >
                <ShoppingBag size={18} className={activeTab === 'orders' ? 'text-white' : ''} />
                <span className="font-medium">Orders</span>
              </button>
              
              <button
                onClick={() => setActiveTab('settings')}
                className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg transition-all duration-200 ${activeTab === 'settings' ? 'bg-brand-primary text-white shadow-md' : 'hover:bg-brand-light text-brand-secondary hover:text-brand-primary'}`}
              >
                <Settings size={18} className={activeTab === 'settings' ? 'text-white' : ''} />
                <span className="font-medium">Settings</span>
              </button>

              {/* Admin Dashboard Button - Only show for admin users */}
              {user?.role === 'admin' && (
                <Link
                  to="/admin"
                  className="w-full flex items-center space-x-3 px-4 py-3 rounded-lg transition-all duration-200 bg-gradient-to-r from-emerald-500 to-green-600 text-white hover:from-emerald-600 hover:to-green-700 shadow-md hover:shadow-lg transform hover:scale-105"
                >
                  <LayoutDashboard size={18} className="text-white" />
                  <span className="font-medium">Admin Dashboard</span>
                  <ExternalLink size={14} className="ml-auto text-white/80" />
                </Link>
              )}

              <div className="pt-4 mt-4 border-t border-brand-light">
                <button 
                  onClick={handleLogout}
                  className="w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-red-600 hover:bg-red-50 transition-all duration-200 font-medium"
                >
                  <LogOut size={18} />
                  <span>Logout</span>
                </button>
              </div>
            </div>
          </div>
        </div>
        
        {/* Main Content */}
        <div className="w-full md:w-3/4">
          {activeTab === 'profile' && (
            <div className="bg-white rounded-xl shadow-lg border border-brand-light p-8">
              <h1 className="text-2xl font-bold text-brand-dark mb-8 pb-4 border-b border-brand-light">Profile Information</h1>
              
              {user ? (
                <div className="space-y-8">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div className="bg-brand-light bg-opacity-30 p-4 rounded-lg">
                      <h3 className="text-sm font-medium text-brand-secondary uppercase tracking-wider mb-2">First Name</h3>
                      <p className="text-lg font-medium text-brand-dark">{user.firstName}</p>
                    </div>
                    
                    <div className="bg-brand-light bg-opacity-30 p-4 rounded-lg">
                      <h3 className="text-sm font-medium text-brand-secondary uppercase tracking-wider mb-2">Last Name</h3>
                      <p className="text-lg font-medium text-brand-dark">{user.lastName}</p>
                    </div>
                    
                    <div className="bg-brand-light bg-opacity-30 p-4 rounded-lg">
                      <h3 className="text-sm font-medium text-brand-secondary uppercase tracking-wider mb-2">Email Address</h3>
                      <p className="text-lg font-medium text-brand-dark">{user.email}</p>
                    </div>
                    
                    <div className="bg-brand-light bg-opacity-30 p-4 rounded-lg">
                      <h3 className="text-sm font-medium text-brand-secondary uppercase tracking-wider mb-2">Account Type</h3>
                      <p className="text-lg font-medium text-brand-dark flex items-center">
                        <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${user.role === 'admin' ? 'bg-brand-primary text-white' : 'bg-brand-primary text-white'}`}>
                          {user.role === 'admin' ? 'Administrator' : 'Customer'}
                        </span>
                      </p>
                    </div>
                  </div>
                  
                  <div className="pt-8 mt-4 border-t border-brand-light">
                    <h3 className="text-lg font-medium text-brand-dark mb-6">Account Actions</h3>
                    <div className="flex flex-wrap gap-4">
                      <button 
                        className="inline-flex items-center px-5 py-2.5 border border-brand-primary rounded-lg shadow-sm text-sm font-medium text-white bg-brand-primary hover:bg-brand-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-primary transition-all duration-200 transform hover:scale-105"
                        onClick={() => setActiveTab('settings')}
                      >
                        <Settings className="-ml-1 mr-2 h-5 w-5" />
                        Edit Profile
                      </button>
                      
                      <button 
                        className="inline-flex items-center px-5 py-2.5 border border-brand-primary rounded-lg shadow-sm text-sm font-medium text-brand-primary bg-white hover:bg-brand-light focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-primary transition-all duration-200 transform hover:scale-105"
                        onClick={() => setActiveTab('orders')}
                      >
                        <ShoppingBag className="-ml-1 mr-2 h-5 w-5 text-brand-primary" />
                        View Orders
                      </button>
                    </div>
                  </div>

                  {/* Admin Dashboard Section - Only show for admin users */}
                  {user?.role === 'admin' && (
                    <div className="mt-8 bg-gradient-to-br from-emerald-50 to-green-50 rounded-xl shadow-lg overflow-hidden border border-emerald-200">
                      <div className="px-6 py-4 bg-gradient-to-r from-emerald-600 to-green-600 border-b border-emerald-600">
                        <h2 className="text-xl font-semibold text-white flex items-center">
                          <LayoutDashboard className="mr-2 h-6 w-6" />
                          Admin Dashboard Access
                        </h2>
                      </div>
                      <div className="p-6">
                        <p className="text-gray-700 mb-6">
                          As an administrator, you have access to the admin dashboard where you can manage products, orders, users, and view analytics.
                        </p>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <Link
                            to="/admin"
                            className="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-lg shadow-md text-white bg-gradient-to-r from-emerald-600 to-green-600 hover:from-emerald-700 hover:to-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 transition-all duration-200 transform hover:scale-105"
                          >
                            <LayoutDashboard className="mr-2 h-5 w-5" />
                            Open Admin Dashboard
                          </Link>
                          <div className="grid grid-cols-2 gap-2 text-sm">
                            <Link to="/admin/products" className="text-emerald-600 hover:text-emerald-700 font-medium flex items-center">
                              <span className="mr-1">📦</span> Products
                            </Link>
                            <Link to="/admin/orders" className="text-emerald-600 hover:text-emerald-700 font-medium flex items-center">
                              <span className="mr-1">🛒</span> Orders
                            </Link>
                            <Link to="/admin/users" className="text-emerald-600 hover:text-emerald-700 font-medium flex items-center">
                              <span className="mr-1">👥</span> Users
                            </Link>
                            <Link to="/admin" className="text-emerald-600 hover:text-emerald-700 font-medium flex items-center">
                              <span className="mr-1">📊</span> Analytics
                            </Link>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <div className="flex flex-col justify-center items-center h-64 space-y-4">
                  <div className="animate-spin rounded-full h-12 w-12 border-4 border-brand-light border-t-4 border-t-brand-primary shadow-md"></div>
                  <p className="text-brand-secondary font-medium">Loading your profile...</p>
                </div>
              )}
            </div>
          )}
          
          {activeTab === 'payments' && (
            <div className="space-y-6">
              <div className="bg-white rounded-xl shadow-lg border border-brand-light p-8">
                <h1 className="text-2xl font-bold text-brand-dark mb-8 pb-4 border-b border-brand-light">Payment History</h1>
                <p className="text-brand-secondary mb-6">View and verify your payment history. All test payments are processed through Stripe's test environment.</p>
                
                <div className="bg-brand-light rounded-lg border-l-4 border-brand-primary p-5 text-brand-primary mb-8">
                  <p className="font-medium text-lg mb-2">Test Mode Information</p>
                  <p>All payments shown here are test payments. No real charges have been made.</p>
                  <p className="mt-2">For testing, you can use Stripe's test card: <span className="font-mono bg-white px-2 py-1 rounded border border-brand-light">4242 4242 4242 4242</span> with any future expiration date and any CVC.</p>
                </div>
              
                <PaymentVerificationDetails showTitle={false} />
              
                <div className="mt-8 bg-white rounded-xl shadow-md overflow-hidden border border-brand-light">
                  <div className="px-6 py-4 bg-brand-primary border-b border-brand-primary">
                    <h2 className="text-xl font-semibold text-white">Stripe Dashboard Access</h2>
                  </div>
                  <div className="p-6">
                    <p className="text-brand-secondary mb-6">As a merchant, you can access your Stripe Dashboard to view detailed payment information, manage refunds, and more.</p>
                    <a 
                      href="https://dashboard.stripe.com/test/payments"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center px-5 py-2.5 border border-transparent text-sm font-medium rounded-lg shadow-md text-white bg-brand-primary hover:bg-brand-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-primary transition-all duration-200 transform hover:scale-105"
                    >
                      <ExternalLink className="mr-2 h-5 w-5" />
                      Open Stripe Dashboard
                    </a>
                  </div>
                </div>
              </div>
            </div>
          )}
          
          {activeTab === 'orders' && (
            <div className="space-y-6">
              <div className="bg-white rounded-xl shadow-lg border border-brand-light p-8">
                <h1 className="text-2xl font-bold text-brand-dark mb-8 pb-4 border-b border-brand-light">My Orders</h1>
                <p className="text-brand-secondary mb-6">View and track your order history.</p>
              
                {error && (
                  <div className="bg-red-50 border-l-4 border-red-500 p-5 mb-6 rounded-lg shadow-sm">
                    <div className="flex items-center">
                      <AlertCircle className="h-6 w-6 text-red-500 mr-3 flex-shrink-0" />
                      <p className="text-red-700 font-medium">{error}</p>
                    </div>
                  </div>
                )}
              
                {loading ? (
                  <div className="flex flex-col justify-center items-center h-64 space-y-4">
                    <div className="animate-spin rounded-full h-12 w-12 border-4 border-brand-light border-t-4 border-t-brand-primary shadow-md"></div>
                    <p className="text-brand-secondary font-medium">Loading your orders...</p>
                  </div>
                ) : orders.length === 0 ? (
                  <div className="bg-white rounded-xl shadow-md p-8 text-center border border-brand-light">
                    <div className="bg-brand-light w-24 h-24 rounded-full flex items-center justify-center mx-auto mb-6">
                      <ShoppingBag className="h-12 w-12 text-brand-primary" />
                    </div>
                    <h3 className="text-xl font-bold text-brand-dark mb-3">No Orders Yet</h3>
                    <p className="text-brand-secondary mb-6 max-w-md mx-auto">You haven't placed any orders yet. Browse our products and start shopping today!</p>
                    <Link 
                      to="/shop"
                      className="inline-flex items-center px-5 py-2.5 border border-transparent rounded-lg shadow-md text-sm font-medium text-white bg-brand-primary hover:bg-brand-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-primary transition-all duration-200 transform hover:scale-105"
                    >
                      <ShoppingBag className="mr-2 h-5 w-5" />
                      Start Shopping
                    </Link>
                  </div>
                ) : (
                  <div className="bg-white rounded-xl shadow-md overflow-hidden border border-brand-light">
                    <div className="overflow-x-auto">
                      <table className="min-w-full divide-y divide-brand-light">
                        <thead className="bg-brand-primary">
                          <tr>
                            <th className="px-6 py-4 text-left text-xs font-medium text-white uppercase tracking-wider">
                              Order ID
                            </th>
                            <th className="px-6 py-4 text-left text-xs font-medium text-white uppercase tracking-wider">
                              Date
                            </th>
                            <th className="px-6 py-4 text-left text-xs font-medium text-white uppercase tracking-wider">
                              Total
                            </th>
                            <th className="px-6 py-4 text-left text-xs font-medium text-white uppercase tracking-wider">
                              Status
                            </th>
                            <th className="px-6 py-4 text-left text-xs font-medium text-white uppercase tracking-wider">
                              Items
                            </th>
                            <th className="px-6 py-4 text-left text-xs font-medium text-white uppercase tracking-wider">
                              Actions
                            </th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-brand-light">
                          {orders.map((order) => {
                            // Safety checks for order data
                            if (!order || !order._id) {
                              console.warn('Invalid order data:', order);
                              return null;
                            }

                            return (
                            <tr key={order._id} className="hover:bg-brand-light transition-colors duration-150">
                              <td className="px-6 py-4 whitespace-nowrap">
                                <span className="text-sm font-medium text-brand-primary">
                                  #{order._id ? order._id.slice(-8).toUpperCase() : 'N/A'}
                                </span>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <span className="text-sm text-brand-secondary">
                                  {order.createdAt ? new Date(order.createdAt).toLocaleDateString('en-US', {
                                    year: 'numeric',
                                    month: 'short',
                                    day: 'numeric',
                                  }) : 'N/A'}
                                </span>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <span className="text-sm font-medium text-brand-dark">
                                  ${(order.total || 0).toFixed(2)}
                                </span>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <span className={`px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                  order.status === 'delivered' ? 'bg-green-100 text-green-800' :
                                  order.status === 'shipped' ? 'bg-blue-100 text-blue-800' :
                                  order.status === 'processing' ? 'bg-yellow-100 text-yellow-800' :
                                  'bg-brand-light text-brand-primary'
                                }`}>
                                  {order.status ? order.status.charAt(0).toUpperCase() + order.status.slice(1) : 'Pending'}
                                </span>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <span className="inline-flex items-center text-sm text-brand-secondary">
                                  <ShoppingBag className="h-4 w-4 mr-1 text-brand-primary" />
                                  {order.items.length} {order.items.length === 1 ? 'item' : 'items'}
                                </span>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="flex space-x-2">
                                  <Link
                                    to={`/refund/${order._id}`}
                                    className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-green-600 bg-green-100 hover:bg-green-200 transition-colors"
                                  >
                                    Request Refund
                                  </Link>
                                  <Link
                                    to="/refund"
                                    className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-gray-600 bg-gray-100 hover:bg-gray-200 transition-colors"
                                  >
                                    View Refunds
                                  </Link>
                                </div>
                              </td>
                            </tr>
                            );
                          })}
                        </tbody>
                      </table>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
          
          {activeTab === 'settings' && (
            <div className="bg-white rounded-xl shadow-lg border border-brand-light p-8">
              <h1 className="text-2xl font-bold text-brand-dark mb-6 pb-3 border-b border-brand-light">Account Settings</h1>
              
              <form className="space-y-6" onSubmit={(e): void => e.preventDefault()}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="firstName" className="block text-sm font-medium text-brand-secondary mb-2">
                      First Name
                    </label>
                    <input
                      type="text"
                      id="firstName"
                      className="w-full px-3 py-2.5 border border-brand-light rounded-md focus:outline-none focus:ring-brand-primary focus:border-brand-primary"
                      defaultValue={user?.firstName}
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="lastName" className="block text-sm font-medium text-brand-secondary mb-2">
                      Last Name
                    </label>
                    <input
                      type="text"
                      id="lastName"
                      className="w-full px-3 py-2.5 border border-brand-light rounded-md focus:outline-none focus:ring-brand-primary focus:border-brand-primary"
                      defaultValue={user?.lastName}
                    />
                  </div>
                </div>
                
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-brand-secondary mb-2">
                    Email Address
                  </label>
                  <input
                    type="email"
                    id="email"
                    className="w-full px-3 py-2.5 border border-brand-light rounded-md focus:outline-none focus:ring-brand-primary focus:border-brand-primary bg-gray-100"
                    defaultValue={user?.email}
                    disabled
                  />
                  <p className="mt-2 text-xs text-brand-secondary italic">Email cannot be changed</p>
                </div>
                
                <div className="border-t border-brand-light pt-8 mt-8">
                  <h2 className="text-xl font-semibold mb-6 text-brand-dark">Change Password</h2>
                  
                  <div className="space-y-6">
                    <div>
                      <label htmlFor="currentPassword" className="block text-sm font-medium text-brand-secondary mb-2">
                        Current Password
                      </label>
                      <input
                        type="password"
                        id="currentPassword"
                        className="w-full px-3 py-2.5 border border-brand-light rounded-md focus:outline-none focus:ring-brand-primary focus:border-brand-primary"
                      />
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label htmlFor="newPassword" className="block text-sm font-medium text-brand-secondary mb-2">
                          New Password
                        </label>
                        <input
                          type="password"
                          id="newPassword"
                          className="w-full px-3 py-2.5 border border-brand-light rounded-md focus:outline-none focus:ring-brand-primary focus:border-brand-primary"
                        />
                      </div>
                      
                      <div>
                        <label htmlFor="confirmPassword" className="block text-sm font-medium text-brand-secondary mb-2">
                          Confirm New Password
                        </label>
                        <input
                          type="password"
                          id="confirmPassword"
                          className="w-full px-3 py-2.5 border border-brand-light rounded-md focus:outline-none focus:ring-brand-primary focus:border-brand-primary"
                        />
                      </div>
                    </div>
                    
                    <div className="pt-6 border-t border-brand-light flex justify-end">
                      <button
                        type="submit"
                        className="inline-flex items-center px-6 py-2.5 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-brand-primary hover:bg-brand-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-primary transition-colors duration-200"
                      >
                        <Save className="h-4 w-4 mr-2" />
                        Save Changes
                      </button>
                    </div>
                    
                    <div className="mt-6 text-sm text-brand-secondary italic bg-gray-50 p-3 rounded-md border border-brand-light">
                      <div className="flex items-start">
                        <Info className="h-5 w-5 text-brand-primary mr-2 flex-shrink-0 mt-0.5" />
                        <p>Note: Profile update functionality is not fully implemented in this demo.</p>
                      </div>
                    </div>
                  </div>
                </div>
              </form>
            </div>
          )}
        </div>
      </div>

      {/* Logout Confirmation Dialog */}
      <ConfirmDialog
        isOpen={showLogoutConfirm}
        onCancel={() => setShowLogoutConfirm(false)}
        onConfirm={confirmLogout}
        title="Confirm Logout"
        message="Are you sure you want to logout? You will need to sign in again to access your account."
        confirmText="Logout"
        cancelText="Cancel"
        type="warning"
      />
    </div>
  );
}