const mongoose = require('mongoose');
const Product = require('../models/Product');
const Order = require('../models/Order');
const User = require('../models/User');
const TaxRule = require('../models/TaxRule');
const Discount = require('../models/Discount');

// Load environment variables
require('dotenv').config();

const MONGODB_URI = process.env.MONGODB_URI;

if (!MONGODB_URI) {
  console.error('MONGODB_URI not found in environment variables');
  process.exit(1);
}

async function connectDB() {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
}

async function migrateProducts() {
  console.log('\n🔄 Migrating Products...');
  
  try {
    const products = await Product.find({});
    let updatedCount = 0;
    
    for (const product of products) {
      let needsUpdate = false;
      
      // Add SKU if missing
      if (!product.sku) {
        product.sku = `SKU-${product._id.toString().slice(-8).toUpperCase()}`;
        needsUpdate = true;
      }
      
      // Add default values for new fields
      if (product.discountType === undefined) {
        product.discountType = 'none';
        product.discountValue = 0;
        needsUpdate = true;
      }
      
      if (product.taxable === undefined) {
        product.taxable = true;
        product.taxCategory = 'standard';
        needsUpdate = true;
      }
      
      if (product.returnable === undefined) {
        product.returnable = true;
        product.returnWindow = 30;
        product.returnPolicy = 'Standard return policy applies';
        needsUpdate = true;
      }
      
      if (product.requiresShipping === undefined) {
        product.requiresShipping = true;
        product.shippingClass = 'standard';
        needsUpdate = true;
      }
      
      if (product.lowStockThreshold === undefined) {
        product.lowStockThreshold = 5;
        product.trackInventory = true;
        needsUpdate = true;
      }
      
      if (product.status === undefined) {
        product.status = product.inStock ? 'active' : 'inactive';
        needsUpdate = true;
      }
      
      // Generate brand if missing or "Unknown"
      if (!product.brand || product.brand === 'Unknown') {
        product.brand = generateBrandFromProduct(product);
        needsUpdate = true;
      }
      
      if (needsUpdate) {
        await product.save();
        updatedCount++;
      }
    }
    
    console.log(`✅ Updated ${updatedCount} products`);
  } catch (error) {
    console.error('❌ Error migrating products:', error);
  }
}

async function migrateOrders() {
  console.log('\n🔄 Migrating Orders...');
  
  try {
    const orders = await Order.find({});
    let updatedCount = 0;
    
    for (const order of orders) {
      let needsUpdate = false;
      
      // Generate order number if missing
      if (!order.orderNumber) {
        order.orderNumber = `ORD-${Date.now()}-${order._id.toString().slice(-6).toUpperCase()}`;
        needsUpdate = true;
      }
      
      // Add default values for new fields
      if (order.discountAmount === undefined) {
        order.discountAmount = 0;
        order.discountType = 'fixed_amount';
        needsUpdate = true;
      }
      
      if (order.totalRefunded === undefined) {
        order.totalRefunded = 0;
        needsUpdate = true;
      }
      
      if (order.fulfillmentStatus === undefined) {
        order.fulfillmentStatus = order.status === 'delivered' ? 'fulfilled' : 'unfulfilled';
        needsUpdate = true;
      }
      
      if (order.riskLevel === undefined) {
        order.riskLevel = 'low';
        needsUpdate = true;
      }
      
      if (order.taxExempt === undefined) {
        order.taxExempt = false;
        needsUpdate = true;
      }
      
      // Initialize arrays if missing
      if (!order.refundRequests) {
        order.refundRequests = [];
        needsUpdate = true;
      }
      
      if (!order.taxBreakdown) {
        order.taxBreakdown = [];
        needsUpdate = true;
      }
      
      if (needsUpdate) {
        await order.save();
        updatedCount++;
      }
    }
    
    console.log(`✅ Updated ${updatedCount} orders`);
  } catch (error) {
    console.error('❌ Error migrating orders:', error);
  }
}

async function createDefaultTaxRules() {
  console.log('\n🔄 Creating Default Tax Rules...');
  
  try {
    const existingRules = await TaxRule.countDocuments();
    if (existingRules > 0) {
      console.log('⚠️ Tax rules already exist, skipping creation');
      return;
    }
    
    // Create default admin user for tax rules
    const adminUser = await User.findOne({ role: 'admin' });
    if (!adminUser) {
      console.log('⚠️ No admin user found, skipping tax rule creation');
      return;
    }
    
    const defaultTaxRules = [
      {
        name: 'GST/HST - Canada',
        description: 'Goods and Services Tax for Canada',
        type: 'percentage',
        rate: 5,
        countries: ['Canada'],
        reportingCategory: 'gst',
        isActive: true,
        createdBy: adminUser._id
      },
      {
        name: 'Provincial Tax - Ontario',
        description: 'Provincial Sales Tax for Ontario',
        type: 'percentage',
        rate: 8,
        countries: ['Canada'],
        provinces: ['Ontario'],
        reportingCategory: 'sales_tax',
        isActive: true,
        createdBy: adminUser._id
      },
      {
        name: 'US Sales Tax',
        description: 'Standard US Sales Tax',
        type: 'percentage',
        rate: 8.25,
        countries: ['United States'],
        reportingCategory: 'sales_tax',
        isActive: false, // Disabled by default
        createdBy: adminUser._id
      }
    ];
    
    await TaxRule.insertMany(defaultTaxRules);
    console.log(`✅ Created ${defaultTaxRules.length} default tax rules`);
  } catch (error) {
    console.error('❌ Error creating tax rules:', error);
  }
}

async function createSampleDiscounts() {
  console.log('\n🔄 Creating Sample Discounts...');
  
  try {
    const existingDiscounts = await Discount.countDocuments();
    if (existingDiscounts > 0) {
      console.log('⚠️ Discounts already exist, skipping creation');
      return;
    }
    
    const adminUser = await User.findOne({ role: 'admin' });
    if (!adminUser) {
      console.log('⚠️ No admin user found, skipping discount creation');
      return;
    }
    
    const sampleDiscounts = [
      {
        name: 'Welcome Discount',
        code: 'WELCOME10',
        description: '10% off for new customers',
        type: 'percentage',
        value: 10,
        minimumOrderAmount: 50,
        maximumDiscountAmount: 100,
        usageLimit: { total: 1000, perUser: 1 },
        validFrom: new Date(),
        validUntil: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year
        isActive: true,
        isPublic: true,
        createdBy: adminUser._id
      },
      {
        name: 'Free Shipping',
        code: 'FREESHIP',
        description: 'Free shipping on orders over $75',
        type: 'free_shipping',
        value: 0,
        minimumOrderAmount: 75,
        freeShippingThreshold: 75,
        usageLimit: { perUser: 5 },
        validFrom: new Date(),
        validUntil: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
        isActive: true,
        isPublic: true,
        createdBy: adminUser._id
      }
    ];
    
    await Discount.insertMany(sampleDiscounts);
    console.log(`✅ Created ${sampleDiscounts.length} sample discounts`);
  } catch (error) {
    console.error('❌ Error creating discounts:', error);
  }
}

function generateBrandFromProduct(product) {
  const categoryBrands = {
    'Electronics': 'TechCorp',
    'Home & Office': 'HomeStyle',
    'Home & Kitchen': 'KitchenPro',
    'Photography': 'PhotoGear',
    'Lifestyle': 'LifeStyle',
    'Home & Decor': 'DecorPlus',
    'Fitness': 'FitGear',
    'Accessories': 'AccessPlus'
  };
  
  return categoryBrands[product.category] || 'Generic Brand';
}

async function runMigration() {
  console.log('🚀 Starting Database Migration...');
  
  await connectDB();
  
  await migrateProducts();
  await migrateOrders();
  await createDefaultTaxRules();
  await createSampleDiscounts();
  
  console.log('\n✅ Migration completed successfully!');
  console.log('\n📋 Summary:');
  console.log('- Enhanced Product schema with discount, tax, and return fields');
  console.log('- Enhanced Order schema with refund, discount, and tax tracking');
  console.log('- Created default tax rules for Canada and US');
  console.log('- Created sample discount codes');
  console.log('\n🎉 Your e-commerce platform is now ready for professional use!');
  
  process.exit(0);
}

// Run migration if this file is executed directly
if (require.main === module) {
  runMigration().catch(error => {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  });
}

module.exports = {
  runMigration,
  migrateProducts,
  migrateOrders,
  createDefaultTaxRules,
  createSampleDiscounts
};
