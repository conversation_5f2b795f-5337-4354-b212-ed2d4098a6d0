const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const dotenv = require('dotenv');
const path = require('path');
const connectDB = require('./config/db');

// Load environment variables
dotenv.config();

// Initialize Stripe with the secret key
const stripeKey = process.env.VITE_STRIPE_SECRET_KEY || process.env.REACT_APP_STRIPE_SECRET_KEY;

// Ensure the key is available
if (!stripeKey) {
  console.error('Stripe secret key is missing. Please check your .env file.');
}

// Import routes
const paymentRoutes = require('./routes/payment');
const userRoutes = require('./routes/users');
const productRoutes = require('./routes/products');
const orderRoutes = require('./routes/orders');
const statsRoutes = require('./routes/stats');
const reviewRoutes = require('./routes/reviews');
const analyticsRoutes = require('./routes/analytics');
const refundRoutes = require('./routes/refunds');
const discountRoutes = require('./routes/discounts');
const taxRoutes = require('./routes/taxes');

const app = express();
const PORT = process.env.PORT || 4000;

// Middleware
app.use(cors());
app.use(bodyParser.json());

// Initialize MongoDB connection at startup
let isConnected = false;

const initializeDB = async () => {
  if (!isConnected) {
    try {
      await connectDB();
      isConnected = true;
      console.log('MongoDB connection initialized');
    } catch (error) {
      console.error('Failed to connect to MongoDB:', error.message);
      throw error;
    }
  }
};

// Initialize database connection immediately
initializeDB().catch(error => {
  console.error('Failed to initialize database:', error);
  process.exit(1);
});

// Middleware to check DB connection (lightweight check)
const ensureDBConnection = (req, res, next) => {
  if (!isConnected) {
    return res.status(500).json({ error: 'Database not connected' });
  }
  next();
};

// In serverless environment, we don't need to create directories
// Files are uploaded to Vercel Blob storage instead

// Root endpoint
app.get('/', (req, res) => {
  res.status(200).json({
    message: 'Jaisal Go Online Backend API',
    status: 'running',
    endpoints: [
      '/api/health',
      '/api/products',
      '/api/users',
      '/api/payment',
      '/api/orders',
      '/api/stats',
      '/api/reviews',
      '/api/analytics',
      '/api/refunds',
      '/api/discounts',
      '/api/taxes'
    ]
  });
});

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.status(200).json({
    status: 'ok',
    message: 'API is healthy',
    timestamp: new Date().toISOString(),
    env: {
      hasStripeKey: !!process.env.VITE_STRIPE_SECRET_KEY,
      hasMongoUri: !!process.env.MONGODB_URI,
      hasJwtSecret: !!process.env.JWT_SECRET,
      hasBlobToken: !!process.env.BLOB_READ_WRITE_TOKEN,
      nodeEnv: process.env.NODE_ENV
    }
  });
});

// Simple test endpoint that doesn't require database
app.get('/api/test', (req, res) => {
  res.status(200).json({
    message: 'Server is working!',
    timestamp: new Date().toISOString(),
    method: req.method,
    url: req.url
  });
});

// Use routes with database connection middleware
try {
  app.use('/api/payment', ensureDBConnection, paymentRoutes);
  app.use('/api/users', ensureDBConnection, userRoutes);
  app.use('/api/products', ensureDBConnection, productRoutes);
  app.use('/api/orders', ensureDBConnection, orderRoutes);
  app.use('/api/stats', ensureDBConnection, statsRoutes);
  app.use('/api/reviews', ensureDBConnection, reviewRoutes);
  app.use('/api/analytics', ensureDBConnection, analyticsRoutes);
  app.use('/api/refunds', ensureDBConnection, refundRoutes);
  app.use('/api/discounts', ensureDBConnection, discountRoutes);
  app.use('/api/taxes', ensureDBConnection, taxRoutes);
} catch (error) {
  console.error('Error setting up routes:', error);
}

// Global error handler
app.use((error, req, res, next) => {
  console.error('Global error handler:', error);
  res.status(500).json({
    error: 'Internal Server Error',
    message: error.message,
    path: req.path
  });
});

// Stripe Dashboard redirect for test mode
app.get('/api/stripe-dashboard', (req, res) => {
  res.redirect('https://dashboard.stripe.com/test/payments');
});

// Serve uploaded files statically
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// Static files are served by the frontend deployment
// This backend only handles API routes

// Start the server (only in development)
if (process.env.NODE_ENV !== 'production') {
  app.listen(PORT, () => {
    console.log(`Server running on port ${PORT}`);
  });
}

// Export the Express app for Vercel
module.exports = app;