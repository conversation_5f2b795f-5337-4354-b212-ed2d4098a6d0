import { Award, Users, Target, Heart, Truck, Shield } from 'lucide-react';

export function About() {
  const features = [
    {
      icon: Award,
      title: 'Premium Quality',
      description: 'We source only the finest products from trusted manufacturers worldwide.',
      color: 'from-yellow-500 to-orange-500'
    },
    {
      icon: Users,
      title: 'Expert Team',
      description: 'Our experienced team carefully curates every product in our collection.',
      color: 'from-blue-500 to-indigo-500'
    },
    {
      icon: Target,
      title: 'Customer Focus',
      description: 'Your satisfaction is our priority. We go above and beyond to exceed expectations.',
      color: 'from-green-500 to-emerald-500'
    },
    {
      icon: Heart,
      title: 'Passion Driven',
      description: 'We are passionate about bringing you products that enhance your lifestyle.',
      color: 'from-red-500 to-pink-500'
    },
    {
      icon: Truck,
      title: 'Fast Delivery',
      description: 'Quick and reliable shipping to get your products to you as soon as possible.',
      color: 'from-purple-500 to-violet-500'
    },
    {
      icon: Shield,
      title: 'Secure Shopping',
      description: 'Shop with confidence knowing your data and transactions are protected.',
      color: 'from-teal-500 to-cyan-500'
    }
  ];



  return (
    <div className="min-h-screen bg-white">
      {/* Story Section */}
      <section className="py-12 lg:py-16 bg-gradient-to-br from-gray-50 to-green-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-center">
            <div className="order-2 lg:order-1">
              <div className="inline-flex items-center px-4 py-2 bg-green-100 text-green-700 rounded-full font-semibold text-sm mb-6">
                📖 Our Journey
              </div>
              <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-6 leading-tight">
                Our Story of
                <span className="text-green-600"> Excellence</span>
              </h2>
              <div className="space-y-4 text-base text-gray-700 leading-relaxed">
                <p>
                  Founded in 2020 with a vision to revolutionize online shopping, jaisalgoonline has grown from a small startup to a trusted destination for premium products. Our journey began with a simple belief: everyone deserves access to quality products at fair prices.
                </p>
                <p>
                  What started as a passion project has evolved into a comprehensive marketplace connecting customers with the finest products worldwide. We believe that quality products have the power to enhance people's lives and experiences.
                </p>
                <p>
                  Today, we serve thousands of satisfied customers globally, maintaining our commitment to excellence, innovation, and customer satisfaction in everything we do.
                </p>
              </div>

              {/* Mission Statement */}
              <div className="mt-6 p-4 bg-white rounded-2xl shadow-lg border-l-4 border-green-500">
                <h3 className="text-lg font-bold text-gray-900 mb-2">Our Mission</h3>
                <p className="text-gray-700 leading-relaxed text-sm">
                  To curate and deliver premium products that enhance our customers' lives while providing exceptional service and value.
                </p>
              </div>
            </div>

            <div className="order-1 lg:order-2">
              <div className="relative">
                <img
                  src="https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?ixlib=rb-4.0.3&auto=format&fit=crop&w=2340&q=80"
                  alt="Our team working together"
                  className="w-full h-[400px] object-cover rounded-3xl shadow-2xl"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 lg:py-32 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-20">
            <div className="inline-flex items-center px-4 py-2 bg-green-100 text-green-700 rounded-full font-semibold text-sm mb-6">
              ⭐ Why Choose Us
            </div>
            <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-8 leading-tight">
              What Makes Us
              <span className="text-green-600">Different</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              We're committed to providing exceptional products and services that exceed your expectations at every step of your journey.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="group relative bg-gradient-to-br from-gray-50 to-white p-8 rounded-3xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-3 border border-gray-100">
                {/* Background Pattern */}
                <div className="absolute top-0 right-0 w-32 h-32 opacity-5">
                  <feature.icon className="w-full h-full text-gray-400" />
                </div>

                <div className="relative">
                  <div className={`inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r ${feature.color} text-white rounded-2xl mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg`}>
                    <feature.icon className="h-8 w-8" />
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-4 group-hover:text-green-600 transition-colors duration-300">
                    {feature.title}
                  </h3>
                  <p className="text-gray-600 leading-relaxed text-lg">
                    {feature.description}
                  </p>
                </div>

                {/* Hover Effect Border */}
                <div className="absolute inset-0 rounded-3xl border-2 border-transparent group-hover:border-green-200 transition-colors duration-300"></div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-6">Our Values</h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Quality, integrity, and customer satisfaction guide everything we do.
            We're committed to building lasting relationships with our customers through
            exceptional products and outstanding service.
          </p>
        </div>
      </section>
    </div>
  );
}
