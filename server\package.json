{"name": "stripe-payment-server", "version": "1.0.0", "description": "Server for handling Stripe payment intents", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "migrate": "node scripts/migrate-database.js"}, "dependencies": {"@vercel/blob": "^1.1.1", "bcryptjs": "^2.4.3", "body-parser": "^1.20.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "exceljs": "^4.4.0", "express": "^4.18.2", "json2csv": "^5.0.7", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.3", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "nodemailer": "^6.9.7", "stripe": "^14.5.0"}, "devDependencies": {"nodemon": "^3.0.1"}}