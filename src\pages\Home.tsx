import { useState, useEffect, useMemo } from 'react';
import { Hero } from '../components/Hero';
import { CategoryFilter } from '../components/CategoryFilter';
import { ProductGrid } from '../components/ProductGrid';
import { ProductDetails } from '../components/ProductDetails';
import { Product } from '../types';
import { Loader } from 'lucide-react';
import { API_URL } from '../utils/env';

interface HomeProps {
  searchQuery: string;
}

export function Home({ searchQuery }: HomeProps) {
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [isProductDetailsOpen, setIsProductDetailsOpen] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState('All Products');
  const [products, setProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch categories from API
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await fetch(`${API_URL}/api/products/categories/all`);
        
        if (!response.ok) {
          throw new Error('Failed to fetch categories');
        }
        
        const data: string[] = await response.json();
        setCategories(['All Products', ...data]);
      } catch (err) {
        console.error('Error fetching categories:', err);
        setError(err instanceof Error ? err.message : 'An error occurred');
      }
    };

    fetchCategories();
  }, []);

  // Fetch products from API
  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Fetch all products for the home page
        const response = await fetch(`${API_URL}/api/products`);
        
        if (!response.ok) {
          throw new Error('Failed to fetch products');
        }
        
        const data = await response.json();
        
        // Map API products to match our Product interface
        const mappedProducts = data.products.map((product: any) => ({
          id: product._id || product.id, // Handle both _id (from MongoDB) and id
          name: product.name,
          price: product.price,
          originalPrice: product.originalPrice,
          image: product.image ? (product.image.startsWith('http') || product.image.startsWith('/uploads/') ? product.image : `${API_URL}${product.image}`) : '',
          images: product.images?.map((img: string) => img ? (img.startsWith('http') || img.startsWith('/uploads/') ? img : `${API_URL}${img}`) : '').filter(Boolean),
          description: product.description,
          category: product.category,
          rating: product.rating || 0,
          reviews: product.reviews || 0,
          inStock: product.inStock,
          featured: product.featured
        }));
        
        setProducts(mappedProducts);
      } catch (err) {
        console.error('Error fetching products:', err);
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, []);

  const filteredProducts = useMemo(() => {
    let filtered = products;

    if (selectedCategory !== 'All Products') {
      filtered = filtered.filter(product => product.category === selectedCategory);
    }

    if (searchQuery) {
      filtered = filtered.filter(product =>
        product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (product.description && product.description.toLowerCase().includes(searchQuery.toLowerCase())) ||
        (product.category && product.category.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }

    return filtered;
  }, [products, selectedCategory, searchQuery]);

  const handleViewDetails = (product: Product) => {
    setSelectedProduct(product);
    setIsProductDetailsOpen(true);
  };

  const handleCloseProductDetails = () => {
    setIsProductDetailsOpen(false);
    setSelectedProduct(null);
  };

  const featuredProducts = products.filter(product => product.featured);

  return (
    <div>
      {/* Hero Section */}
      {!searchQuery && selectedCategory === 'All Products' && <Hero />}

      {loading ? (
        <div className="py-16 flex justify-center items-center">
          <Loader className="h-12 w-12 animate-spin text-green-600" />
          <span className="ml-4 text-xl font-medium text-gray-700">Loading products...</span>
        </div>
      ) : error ? (
        <div className="py-16 text-center">
          <div className="bg-red-50 p-6 rounded-lg max-w-2xl mx-auto">
            <h3 className="text-xl font-medium text-red-800 mb-2">Error loading products</h3>
            <p className="text-red-700">{error}</p>
            <button 
              onClick={() => window.location.reload()} 
              className="mt-4 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
            >
              Try Again
            </button>
          </div>
        </div>
      ) : (
        <>
          {/* Featured Products Section */}
          {!searchQuery && selectedCategory === 'All Products' && featuredProducts.length > 0 && (
            <section className="py-16 bg-gradient-to-br from-green-50 to-emerald-50">
              <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="text-center mb-12">
                  <div className="inline-flex items-center px-6 py-3 rounded-full bg-green-600 text-white font-bold text-sm mb-6 shadow-lg">
                    ⭐ Featured Collection
                  </div>
                  <h2 className="text-4xl font-bold text-green-800 mb-6">Featured Products</h2>
                  <p className="text-lg text-gray-700 max-w-3xl mx-auto">
                    Discover our carefully selected premium products that define excellence and innovation.
                  </p>
                </div>
                <ProductGrid products={featuredProducts} onViewDetails={handleViewDetails} />
              </div>
            </section>
          )}

          {/* All Products Section */}
          <section className="py-16 bg-white">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="mb-8">
                <div className="text-center mb-8">
                  <h2 className="text-4xl font-bold text-green-800 mb-4">
                    {searchQuery ? `Search Results for "${searchQuery}"` : 'Complete Collection'}
                  </h2>
                  <div className="inline-flex items-center px-4 py-2 rounded-full bg-green-100 text-green-700 font-semibold">
                    {filteredProducts.length} {filteredProducts.length === 1 ? 'product' : 'products'}
                    {searchQuery ? ' found' : ' available'}
                  </div>
                </div>

                <div className="flex justify-center mb-8">
                  <CategoryFilter
                    categories={categories}
                    selectedCategory={selectedCategory}
                    onCategoryChange={setSelectedCategory}
                  />
                </div>
              </div>

              {filteredProducts.length === 0 ? (
                <div className="text-center py-16">
                  <div className="text-gray-400 mb-4">
                    <svg className="mx-auto h-24 w-24" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8l-4 4-4-4" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No products found</h3>
                  <p className="text-gray-500">Try adjusting your search or filter criteria</p>
                </div>
              ) : (
                <ProductGrid products={filteredProducts} onViewDetails={handleViewDetails} />
              )}
            </div>
          </section>

          {/* Features Section */}
          {!searchQuery && selectedCategory === 'All Products' && (
            <section className="py-16 bg-gradient-to-br from-green-900 via-green-800 to-green-900 text-white">
              <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="text-center mb-12">
                  <h2 className="text-3xl font-bold mb-6">Why Choose jaisalgoonline?</h2>
                  <p className="text-lg text-green-100 max-w-3xl mx-auto">
                    Experience quality shopping with our carefully curated collection of premium products
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                  <div className="text-center">
                    <div className="w-20 h-20 rounded-2xl overflow-hidden mx-auto mb-6 transform hover:scale-110 transition-transform duration-300 shadow-lg">
                      <img
                        src="https://images.unsplash.com/photo-1607083206869-4c7672e72a8a?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                        alt="Quality Products"
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <h3 className="text-xl font-bold mb-4">Quality Products</h3>
                    <p className="text-green-100 leading-relaxed">
                      Every product is carefully selected to meet our high standards for quality and value
                    </p>
                  </div>

                  <div className="text-center">
                    <div className="w-20 h-20 rounded-2xl overflow-hidden mx-auto mb-6 transform hover:scale-110 transition-transform duration-300 shadow-lg">
                      <img
                        src="https://images.unsplash.com/photo-1586528116311-ad8dd3c8310d?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                        alt="Fast Delivery"
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <h3 className="text-xl font-bold mb-4">Fast Delivery</h3>
                    <p className="text-green-100 leading-relaxed">
                      Quick and reliable shipping to get your orders to you as fast as possible
                    </p>
                  </div>

                  <div className="text-center">
                    <div className="w-20 h-20 rounded-2xl overflow-hidden mx-auto mb-6 transform hover:scale-110 transition-transform duration-300 shadow-lg">
                      <img
                        src="https://images.unsplash.com/photo-1559526324-4b87b5e36e44?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                        alt="Customer Satisfaction"
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <h3 className="text-xl font-bold mb-4">Customer Satisfaction</h3>
                    <p className="text-green-100 leading-relaxed">
                      Dedicated customer service and satisfaction guarantee on all purchases
                    </p>
                  </div>
                </div>
              </div>
            </section>
          )}
        </>
      )}

      {/* Product Details Modal */}
      <ProductDetails
        product={selectedProduct}
        isOpen={isProductDetailsOpen}
        onClose={handleCloseProductDetails}
      />
    </div>
  );
}
