/**
 * Product Update Manager
 * Handles real-time product updates across the application
 */

export interface ProductUpdateData {
  productId: string;
  rating?: number;
  reviews?: number;
  timestamp?: number;
}

export class ProductUpdateManager {
  private static instance: ProductUpdateManager;
  private listeners: Map<string, Set<(data: ProductUpdateData) => void>> = new Map();

  private constructor() {}

  static getInstance(): ProductUpdateManager {
    if (!ProductUpdateManager.instance) {
      ProductUpdateManager.instance = new ProductUpdateManager();
    }
    return ProductUpdateManager.instance;
  }

  /**
   * Subscribe to product updates for a specific product
   */
  subscribe(productId: string, callback: (data: ProductUpdateData) => void): () => void {
    if (!this.listeners.has(productId)) {
      this.listeners.set(productId, new Set());
    }
    
    this.listeners.get(productId)!.add(callback);
    
    // Return unsubscribe function
    return () => {
      const productListeners = this.listeners.get(productId);
      if (productListeners) {
        productListeners.delete(callback);
        if (productListeners.size === 0) {
          this.listeners.delete(productId);
        }
      }
    };
  }

  /**
   * Notify all subscribers of a product update
   */
  notifyUpdate(data: ProductUpdateData): void {
    console.log('🔄 ProductUpdateManager: Notifying update for product:', data.productId);
    
    const productListeners = this.listeners.get(data.productId);
    if (productListeners) {
      productListeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error('❌ Error in product update callback:', error);
        }
      });
    }

    // Also dispatch DOM events for backward compatibility
    this.dispatchDOMEvents(data);
  }

  /**
   * Dispatch DOM events for components that still use addEventListener
   */
  private dispatchDOMEvents(data: ProductUpdateData): void {
    // Dispatch the main product review added event
    const reviewAddedEvent = new CustomEvent('productReviewAdded', {
      detail: {
        productId: data.productId,
        newRating: data.rating,
        reviewCount: data.reviews,
        timestamp: data.timestamp || Date.now()
      },
      bubbles: true
    });
    document.dispatchEvent(reviewAddedEvent);

    // Dispatch the rating updated event
    const ratingUpdatedEvent = new CustomEvent('productRatingUpdated', {
      detail: {
        productId: data.productId,
        rating: data.rating,
        reviews: data.reviews
      },
      bubbles: true
    });
    document.dispatchEvent(ratingUpdatedEvent);
  }

  /**
   * Get current listener count for debugging
   */
  getListenerCount(): number {
    let total = 0;
    this.listeners.forEach(listeners => {
      total += listeners.size;
    });
    return total;
  }

  /**
   * Clear all listeners (useful for testing)
   */
  clearAllListeners(): void {
    this.listeners.clear();
  }
}

// Export singleton instance
export const productUpdateManager = ProductUpdateManager.getInstance();

// Helper hook for React components
export function useProductUpdates(productId: string, callback: (data: ProductUpdateData) => void) {
  const React = require('react');
  
  React.useEffect(() => {
    if (!productId) return;
    
    const unsubscribe = productUpdateManager.subscribe(productId, callback);
    
    return unsubscribe;
  }, [productId, callback]);
}
