export interface Review {
  id: string;
  _id?: string; // MongoDB ObjectId
  product: string;
  user: string;
  rating: number;
  title: string;
  comment: string;
  createdAt: string;
  updatedAt: string;
  userName?: string;
}

export interface PricingInfo {
  originalPrice: number;
  effectivePrice: number;
  priceWithTax: number;
  hasDiscount: boolean;
  discountPercentage: number;
  savingsAmount: number;
  taxRate: number;
  taxIncluded: boolean;
  displayPrice: number;
}

export interface Product {
  id: string;
  name: string;
  price: number;
  originalPrice?: number;
  image: string;
  images?: string[];
  description: string;
  category: string;
  brand?: string;
  rating: number;
  reviews: number;
  reviewsRef?: Review[];
  inStock: boolean;
  featured?: boolean;
  quantity?: number;
  taxRate?: number;
  taxIncluded?: boolean;
  pricingInfo?: PricingInfo;
}

export interface CartItem {
  id: string;
  product: Product;
  quantity: number;
}

export interface CartContextType {
  items: CartItem[];
  addItem: (product: Product, quantity?: number) => void;
  removeItem: (productId: string) => void;
  updateQuantity: (productId: string, quantity: number) => void;
  clearCart: () => void;
  totalItems: number;
  totalPrice: number;
  loadUserCart: (userId: string) => void;
}