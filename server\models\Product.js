const mongoose = require('mongoose');

const ProductSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Product name is required'],
    trim: true
  },
  price: {
    type: Number,
    required: [true, 'Product price is required'],
    min: [0, 'Price cannot be negative']
  },
  originalPrice: {
    type: Number,
    min: [0, 'Original price cannot be negative']
  },
  // Discount fields
  discountType: {
    type: String,
    enum: ['none', 'percentage', 'fixed'],
    default: 'none'
  },
  discountValue: {
    type: Number,
    min: [0, 'Discount value cannot be negative'],
    default: 0
  },
  discountStartDate: {
    type: Date
  },
  discountEndDate: {
    type: Date
  },
  // Tax fields
  taxRate: {
    type: Number,
    min: [0, 'Tax rate cannot be negative'],
    max: [100, 'Tax rate cannot exceed 100%'],
    default: 0
  },
  taxIncluded: {
    type: Boolean,
    default: false
  },
  image: {
    type: String,
    required: [true, 'Product image is required']
  },
  images: {
    type: [String],
    default: []
  },
  description: {
    type: String,
    required: [true, 'Product description is required']
  },
  category: {
    type: String,
    required: [true, 'Product category is required']
  },
  brand: {
    type: String,
    trim: true,
    default: 'Unknown'
  },
  rating: {
    type: Number,
    default: 0,
    min: 0,
    max: 5
  },
  reviews: {
    type: Number,
    default: 0
  },
  reviewsRef: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Review'
  }],
  inStock: {
    type: Boolean,
    default: true
  },
  featured: {
    type: Boolean,
    default: false
  },
  quantity: {
    type: Number,
    default: 0,
    min: 0
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  // Enhanced fields for professional e-commerce
  sku: {
    type: String,
    unique: true,
    sparse: true
  },
  weight: {
    type: Number,
    min: 0
  },
  dimensions: {
    length: Number,
    width: Number,
    height: Number,
    unit: {
      type: String,
      enum: ['cm', 'inch'],
      default: 'cm'
    }
  },
  // Tax settings (removed duplicate discount fields)
  taxable: {
    type: Boolean,
    default: true
  },
  taxCategory: {
    type: String,
    default: 'standard'
  },
  // Inventory management
  lowStockThreshold: {
    type: Number,
    default: 5,
    min: 0
  },
  trackInventory: {
    type: Boolean,
    default: true
  },
  // Return/Refund policy
  returnable: {
    type: Boolean,
    default: true
  },
  returnWindow: {
    type: Number,
    default: 30 // days
  },
  returnPolicy: {
    type: String,
    default: 'Standard return policy applies'
  },
  // Shipping
  requiresShipping: {
    type: Boolean,
    default: true
  },
  shippingClass: {
    type: String,
    default: 'standard'
  },
  // SEO and metadata
  metaTitle: String,
  metaDescription: String,
  tags: [String],
  // Status
  status: {
    type: String,
    enum: ['draft', 'active', 'inactive', 'discontinued'],
    default: 'active'
  }
});

// Method to check if discount is currently active
ProductSchema.methods.isDiscountActive = function() {
  if (this.discountType === 'none' || !this.discountValue) {
    return false;
  }

  const now = new Date();
  const startDate = this.discountStartDate ? new Date(this.discountStartDate) : null;
  const endDate = this.discountEndDate ? new Date(this.discountEndDate) : null;

  if (startDate && now < startDate) {
    return false;
  }

  if (endDate && now > endDate) {
    return false;
  }

  return true;
};

// Method to calculate effective price with discount
ProductSchema.methods.getEffectivePrice = function() {
  if (!this.isDiscountActive()) {
    return this.price;
  }

  if (this.discountType === 'percentage') {
    return this.price * (1 - this.discountValue / 100);
  } else if (this.discountType === 'fixed') {
    return Math.max(0, this.price - this.discountValue);
  }

  return this.price;
};

// Method to calculate price with tax
ProductSchema.methods.getPriceWithTax = function() {
  const effectivePrice = this.getEffectivePrice();

  if (this.taxIncluded) {
    return effectivePrice;
  }

  return effectivePrice * (1 + this.taxRate / 100);
};

// Method to get discount percentage for display
ProductSchema.methods.getDiscountPercentage = function() {
  if (!this.isDiscountActive()) {
    return 0;
  }

  if (this.discountType === 'percentage') {
    return this.discountValue;
  } else if (this.discountType === 'fixed') {
    // Calculate percentage based on original price
    return Math.round((this.discountValue / this.price) * 100);
  }

  return 0;
};

// Method to get savings amount
ProductSchema.methods.getSavingsAmount = function() {
  if (!this.isDiscountActive()) {
    return 0;
  }

  return this.price - this.getEffectivePrice();
};

// Method to get complete pricing info for frontend
ProductSchema.methods.getPricingInfo = function() {
  const isDiscountActive = this.isDiscountActive();
  const originalPrice = this.price;
  const effectivePrice = this.getEffectivePrice();
  const priceWithTax = this.getPriceWithTax();
  const discountPercentage = this.getDiscountPercentage();
  const savingsAmount = this.getSavingsAmount();

  return {
    originalPrice: Math.round(originalPrice * 100) / 100,
    effectivePrice: Math.round(effectivePrice * 100) / 100,
    priceWithTax: Math.round(priceWithTax * 100) / 100,
    hasDiscount: isDiscountActive,
    discountPercentage: Math.round(discountPercentage),
    savingsAmount: Math.round(savingsAmount * 100) / 100,
    taxRate: this.taxRate || 0,
    taxIncluded: this.taxIncluded || false,
    displayPrice: this.taxIncluded ? effectivePrice : priceWithTax
  };
};

// Update the updatedAt field on save
ProductSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

module.exports = mongoose.model('Product', ProductSchema);