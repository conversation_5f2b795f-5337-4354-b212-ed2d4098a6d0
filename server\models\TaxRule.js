const mongoose = require('mongoose');

const TaxRuleSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  description: String,
  type: {
    type: String,
    enum: ['percentage', 'fixed_amount'],
    required: true,
    default: 'percentage'
  },
  rate: {
    type: Number,
    required: true,
    min: 0,
    max: 100 // For percentage, max 100%
  },
  // Geographic applicability
  countries: [String],
  states: [String],
  provinces: [String],
  cities: [String],
  zipCodes: [String],
  // Product applicability
  applicableCategories: [String],
  applicableProducts: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Product'
  }],
  excludedCategories: [String],
  excludedProducts: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Product'
  }],
  // Conditions
  minimumOrderAmount: {
    type: Number,
    default: 0,
    min: 0
  },
  maximumOrderAmount: {
    type: Number,
    min: 0
  },
  // Tax behavior
  compoundTax: {
    type: Boolean,
    default: false // Whether this tax is calculated on top of other taxes
  },
  priority: {
    type: Number,
    default: 0 // Higher priority taxes are calculated first
  },
  includeShipping: {
    type: Boolean,
    default: false
  },
  includeDiscounts: {
    type: Boolean,
    default: true // Whether to apply tax after discounts
  },
  // Status and metadata
  isActive: {
    type: Boolean,
    default: true
  },
  validFrom: {
    type: Date,
    default: Date.now
  },
  validUntil: Date,
  // Reporting
  taxCode: String, // For integration with tax services
  reportingCategory: {
    type: String,
    enum: ['sales_tax', 'vat', 'gst', 'excise', 'customs', 'other'],
    default: 'sales_tax'
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

TaxRuleSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Method to check if tax rule applies to a location
TaxRuleSchema.methods.appliesTo = function(location) {
  if (!this.isActive) return false;
  
  const now = new Date();
  if (this.validFrom > now || (this.validUntil && this.validUntil < now)) {
    return false;
  }
  
  // Check geographic applicability
  if (this.countries.length > 0 && !this.countries.includes(location.country)) {
    return false;
  }
  
  if (this.states.length > 0 && !this.states.includes(location.state)) {
    return false;
  }
  
  if (this.provinces.length > 0 && !this.provinces.includes(location.province)) {
    return false;
  }
  
  if (this.cities.length > 0 && !this.cities.includes(location.city)) {
    return false;
  }
  
  if (this.zipCodes.length > 0 && !this.zipCodes.includes(location.zipCode)) {
    return false;
  }
  
  return true;
};

// Method to calculate tax for an order
TaxRuleSchema.methods.calculateTax = function(orderAmount, items = []) {
  if (!this.appliesTo) return 0;
  
  // Check minimum/maximum order amount
  if (orderAmount < this.minimumOrderAmount) return 0;
  if (this.maximumOrderAmount && orderAmount > this.maximumOrderAmount) return 0;
  
  // Calculate applicable amount based on products
  let applicableAmount = orderAmount;
  
  if (this.applicableCategories.length > 0 || this.applicableProducts.length > 0) {
    applicableAmount = items.reduce((sum, item) => {
      const isApplicable = 
        this.applicableCategories.includes(item.category) ||
        this.applicableProducts.some(p => p.toString() === item.product.toString());
      
      const isExcluded = 
        this.excludedCategories.includes(item.category) ||
        this.excludedProducts.some(p => p.toString() === item.product.toString());
      
      return sum + (isApplicable && !isExcluded ? item.price * item.quantity : 0);
    }, 0);
  }
  
  // Calculate tax
  if (this.type === 'percentage') {
    return (applicableAmount * this.rate) / 100;
  } else {
    return this.rate;
  }
};

module.exports = mongoose.model('TaxRule', TaxRuleSchema);
