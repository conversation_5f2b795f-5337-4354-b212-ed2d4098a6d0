const mongoose = require('mongoose');

const StatsSchema = new mongoose.Schema({
  totalUsers: {
    type: Number,
    default: 0
  },
  totalOrders: {
    type: Number,
    default: 0
  },
  totalRevenue: {
    type: Number,
    default: 0
  },
  grossSales: {
    type: Number,
    default: 0
  },
  netSales: {
    type: Number,
    default: 0
  },
  averageOrderValue: {
    type: Number,
    default: 0
  },
  totalRefunds: {
    type: Number,
    default: 0
  },
  refundCount: {
    type: Number,
    default: 0
  },
  refundRate: {
    type: Number,
    default: 0
  },
  totalTaxCollected: {
    type: Number,
    default: 0
  },
  dailyRevenue: [
    {
      date: {
        type: Date,
        required: true
      },
      amount: {
        type: Number,
        default: 0
      }
    }
  ],
  monthlySales: [
    {
      month: {
        type: Number, // 0-11 (Jan-Dec)
        required: true
      },
      year: {
        type: Number,
        required: true
      },
      amount: {
        type: Number,
        default: 0
      },
      orders: {
        type: Number,
        default: 0
      }
    }
  ],
  topSellingProducts: [
    {
      product: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Product'
      },
      quantity: {
        type: Number,
        default: 0
      },
      revenue: {
        type: Number,
        default: 0
      }
    }
  ],
  topCategories: [
    {
      category: {
        type: String,
        required: true
      },
      sales: {
        type: Number,
        default: 0
      }
    }
  ],
  lowPerformingProducts: [
    {
      product: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Product'
      },
      quantity: {
        type: Number,
        default: 0
      },
      revenue: {
        type: Number,
        default: 0
      }
    }
  ],
  salesByBrand: [
    {
      brand: {
        type: String,
        required: true
      },
      sales: {
        type: Number,
        default: 0
      }
    }
  ],
  salesByLocation: [
    {
      location: {
        type: String,
        required: true
      },
      sales: {
        type: Number,
        default: 0
      }
    }
  ],
  salesByProvince: [
    {
      province: {
        type: String,
        required: true
      },
      sales: {
        type: Number,
        default: 0
      }
    }
  ],
  taxByProvince: [
    {
      province: {
        type: String,
        required: true
      },
      tax: {
        type: Number,
        default: 0
      }
    }
  ],
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Update the updatedAt field on save
StatsSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

module.exports = mongoose.model('Stats', StatsSchema);