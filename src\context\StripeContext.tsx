import type { ReactNode } from 'react';
import { loadStripe } from '@stripe/stripe-js';
import { Elements } from '@stripe/react-stripe-js';
import { STRIPE_PUBLISHABLE_KEY, API_URL, IS_DEVELOPMENT } from '../utils/env';

// Load Stripe publishable key from environment variables
const stripePromise = loadStripe(STRIPE_PUBLISHABLE_KEY);

// Ensure the key is available
if (!STRIPE_PUBLISHABLE_KEY) {
  console.error('Stripe publishable key is missing. Please check your .env file.');
}

interface StripeProviderProps {
  children: ReactNode;
}

export function StripeProvider({ children }: StripeProviderProps) {
  return (
    <Elements stripe={stripePromise}>
      {children}
    </Elements>
  );
}

// API function to create a payment intent via the server
export async function createPaymentIntent(amount: number): Promise<{ clientSecret: string, paymentIntentId: string }> {
  try {
    // Ensure amount is an integer before sending to the server
    const amountInteger = Math.round(amount);
    
    // Call the server API to create a payment intent
    const response = await fetch(`${API_URL}/api/payment/create-payment-intent`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ amount: amountInteger }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to create payment intent');
    }

    return await response.json();
  } catch (error) {
    console.error('Error creating payment intent:', error);
    
    // Fallback to mock implementation for development/testing
    if (IS_DEVELOPMENT) {
      console.warn('Using mock payment intent as fallback');
      const mockId = 'pi_mock_' + Math.random().toString(36).substring(2, 15);
      return {
        clientSecret: 'pi_mock_secret_' + Math.random().toString(36).substring(2, 15),
        paymentIntentId: mockId
      };
    }
    
    throw error;
  }
}