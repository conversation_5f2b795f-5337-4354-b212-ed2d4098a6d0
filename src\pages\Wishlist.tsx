import { useWishlist } from '../context/WishlistContext';
import { useCart } from '../context/CartContext';
import { useToast } from '../context/ToastContext';
import { ProductCard } from '../components/ProductCard';
import { ProductDetails } from '../components/ProductDetails';
import { ConfirmDialog } from '../components/ConfirmDialog';
import { Heart, ShoppingBag } from 'lucide-react';
import { useState } from 'react';
import { Product } from '../types';

export function Wishlist() {
  const { wishlistItems, clearWishlist } = useWishlist();
  const { addItem } = useCart();
  const { showToast } = useToast();
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [isProductDetailsOpen, setIsProductDetailsOpen] = useState(false);
  const [showClearConfirm, setShowClearConfirm] = useState(false);
  const [showAddAllConfirm, setShowAddAllConfirm] = useState(false);

  const handleViewDetails = (product: Product) => {
    setSelectedProduct(product);
    setIsProductDetailsOpen(true);
  };

  const handleAddAllToCart = () => {
    const inStockItems = wishlistItems.filter(product => product.inStock);
    if (inStockItems.length === 0) {
      showToast('No items in stock to add to cart', 'warning');
      return;
    }
    setShowAddAllConfirm(true);
  };

  const confirmAddAllToCart = () => {
    const inStockItems = wishlistItems.filter(product => product.inStock);
    inStockItems.forEach(product => {
      addItem(product);
    });
    showToast(`Added ${inStockItems.length} ${inStockItems.length === 1 ? 'item' : 'items'} to cart 🛒`, 'success');
    setShowAddAllConfirm(false);
  };

  const handleClearWishlist = () => {
    if (wishlistItems.length === 0) {
      showToast('Wishlist is already empty', 'info');
      return;
    }
    setShowClearConfirm(true);
  };

  const confirmClearWishlist = () => {
    clearWishlist();
    showToast('Wishlist cleared successfully', 'success');
    setShowClearConfirm(false);
  };

  if (wishlistItems.length === 0) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 to-emerald-50 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center py-20">
            <div className="text-green-400 mb-8">
              <Heart className="mx-auto h-32 w-32" />
            </div>
            <h1 className="text-4xl font-bold text-gray-900 mb-4">Your Wishlist is Empty</h1>
            <p className="text-xl text-gray-600 mb-8">
              Start adding products you love to your wishlist!
            </p>
            <a
              href="/"
              className="inline-flex items-center px-8 py-4 bg-green-600 text-white font-semibold rounded-xl hover:bg-green-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105"
            >
              <ShoppingBag className="h-5 w-5 mr-2" />
              Continue Shopping
            </a>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-emerald-50 py-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center px-6 py-3 rounded-full bg-green-600 text-white font-bold text-sm mb-6 shadow-lg">
            ❤️ Your Wishlist
          </div>
          <h1 className="text-4xl font-bold text-green-800 mb-4">
            My Favorite Products
          </h1>
          <p className="text-lg text-gray-700 mb-8">
            {wishlistItems.length} {wishlistItems.length === 1 ? 'item' : 'items'} in your wishlist
          </p>
          
          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button
              onClick={handleAddAllToCart}
              className="inline-flex items-center px-6 py-3 bg-green-600 text-white font-semibold rounded-xl hover:bg-green-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105"
            >
              <ShoppingBag className="h-5 w-5 mr-2" />
              Add All to Cart
            </button>
            <button
              onClick={handleClearWishlist}
              className="inline-flex items-center px-6 py-3 bg-white text-gray-700 font-semibold rounded-xl hover:bg-gray-50 transition-all duration-300 shadow-lg border border-gray-200"
            >
              Clear Wishlist
            </button>
          </div>
        </div>

        {/* Products Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 lg:gap-8">
          {wishlistItems.map((product, index) => (
            <div
              key={product.id}
              className="animate-scale-in"
              style={{
                animationDelay: `${index * 0.1}s`,
                animationFillMode: 'both'
              }}
            >
              <ProductCard
                product={product}
                onViewDetails={handleViewDetails}
              />
            </div>
          ))}
        </div>
      </div>

      {/* Product Details Modal */}
      {isProductDetailsOpen && selectedProduct && (
        <ProductDetails
          product={selectedProduct}
          isOpen={isProductDetailsOpen}
          onClose={() => {
            setIsProductDetailsOpen(false);
            setSelectedProduct(null);
          }}
        />
      )}

      {/* Confirmation Dialogs */}
      <ConfirmDialog
        isOpen={showClearConfirm}
        title="Clear Wishlist"
        message="Are you sure you want to remove all items from your wishlist? This action cannot be undone."
        confirmText="Clear Wishlist"
        cancelText="Cancel"
        onConfirm={confirmClearWishlist}
        onCancel={() => setShowClearConfirm(false)}
        type="danger"
      />

      <ConfirmDialog
        isOpen={showAddAllConfirm}
        title="Add All to Cart"
        message={`Are you sure you want to add all ${wishlistItems.filter(p => p.inStock).length} in-stock items to your cart?`}
        confirmText="Add to Cart"
        cancelText="Cancel"
        onConfirm={confirmAddAllToCart}
        onCancel={() => setShowAddAllConfirm(false)}
        type="info"
      />
    </div>
  );
}
