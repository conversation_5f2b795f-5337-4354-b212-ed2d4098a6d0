const express = require('express');
const router = express.Router();
const TaxRule = require('../models/TaxRule');
const { protect, admin } = require('../middleware/auth');

/**
 * @route   POST /api/taxes
 * @desc    Create tax rule (Admin)
 * @access  Private/Admin
 */
router.post('/', protect, admin, async (req, res) => {
  try {
    const taxRule = new TaxRule({
      ...req.body,
      createdBy: req.user._id
    });

    await taxRule.save();

    res.status(201).json({
      success: true,
      message: 'Tax rule created successfully',
      taxRule
    });
  } catch (error) {
    console.error('Error creating tax rule:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

/**
 * @route   GET /api/taxes
 * @desc    Get all tax rules (Admin)
 * @access  Private/Admin
 */
router.get('/', protect, admin, async (req, res) => {
  try {
    const { status, type, page = 1, limit = 20 } = req.query;
    
    let filter = {};
    if (status === 'active') {
      filter.isActive = true;
    } else if (status === 'inactive') {
      filter.isActive = false;
    }
    
    if (type) {
      filter.reportingCategory = type;
    }

    const taxRules = await TaxRule.find(filter)
      .populate('createdBy', 'firstName lastName')
      .populate('applicableProducts', 'name')
      .sort({ priority: -1, createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await TaxRule.countDocuments(filter);

    res.json({
      success: true,
      taxRules,
      pagination: {
        page: parseInt(page),
        pages: Math.ceil(total / limit),
        total
      }
    });
  } catch (error) {
    console.error('Error fetching tax rules:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

/**
 * @route   POST /api/taxes/calculate
 * @desc    Calculate taxes for an order
 * @access  Private
 */
router.post('/calculate', protect, async (req, res) => {
  try {
    const { orderAmount, items, shippingAddress, includeShipping = false, shippingAmount = 0 } = req.body;

    // Get applicable tax rules
    const taxRules = await TaxRule.find({ 
      isActive: true,
      validFrom: { $lte: new Date() },
      $or: [
        { validUntil: { $exists: false } },
        { validUntil: { $gte: new Date() } }
      ]
    }).sort({ priority: -1 });

    let totalTax = 0;
    let taxBreakdown = [];
    let taxableAmount = orderAmount;

    if (includeShipping) {
      taxableAmount += shippingAmount;
    }

    // Apply each applicable tax rule
    for (const rule of taxRules) {
      if (rule.appliesTo(shippingAddress)) {
        const taxAmount = rule.calculateTax(taxableAmount, items);
        
        if (taxAmount > 0) {
          totalTax += taxAmount;
          
          taxBreakdown.push({
            name: rule.name,
            rate: rule.rate,
            amount: Math.round(taxAmount * 100) / 100,
            type: rule.type,
            taxCode: rule.taxCode
          });

          // If compound tax, add this tax to the taxable amount for next calculations
          if (rule.compoundTax) {
            taxableAmount += taxAmount;
          }
        }
      }
    }

    res.json({
      success: true,
      totalTax: Math.round(totalTax * 100) / 100,
      taxBreakdown,
      taxableAmount: Math.round(taxableAmount * 100) / 100
    });
  } catch (error) {
    console.error('Error calculating taxes:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

/**
 * @route   PUT /api/taxes/:id
 * @desc    Update tax rule (Admin)
 * @access  Private/Admin
 */
router.put('/:id', protect, admin, async (req, res) => {
  try {
    const taxRule = await TaxRule.findById(req.params.id);
    
    if (!taxRule) {
      return res.status(404).json({ success: false, message: 'Tax rule not found' });
    }

    // Update fields
    Object.keys(req.body).forEach(key => {
      if (key !== '_id' && key !== 'createdBy' && key !== 'createdAt') {
        taxRule[key] = req.body[key];
      }
    });

    await taxRule.save();

    res.json({
      success: true,
      message: 'Tax rule updated successfully',
      taxRule
    });
  } catch (error) {
    console.error('Error updating tax rule:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

/**
 * @route   DELETE /api/taxes/:id
 * @desc    Delete tax rule (Admin)
 * @access  Private/Admin
 */
router.delete('/:id', protect, admin, async (req, res) => {
  try {
    const taxRule = await TaxRule.findById(req.params.id);
    
    if (!taxRule) {
      return res.status(404).json({ success: false, message: 'Tax rule not found' });
    }

    await taxRule.deleteOne();

    res.json({
      success: true,
      message: 'Tax rule deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting tax rule:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

/**
 * @route   GET /api/taxes/stats
 * @desc    Get tax statistics (Admin)
 * @access  Private/Admin
 */
router.get('/stats', protect, admin, async (req, res) => {
  try {
    const { startDate, endDate } = req.query;
    
    let dateFilter = {};
    if (startDate && endDate) {
      dateFilter = {
        createdAt: {
          $gte: new Date(startDate),
          $lte: new Date(endDate)
        }
      };
    }

    // This would typically come from Order collection with tax breakdown
    // For now, we'll return basic stats from tax rules
    const totalRules = await TaxRule.countDocuments();
    const activeRules = await TaxRule.countDocuments({ isActive: true });
    
    const rulesByCategory = await TaxRule.aggregate([
      { $group: { _id: '$reportingCategory', count: { $sum: 1 } } }
    ]);

    res.json({
      success: true,
      stats: {
        totalRules,
        activeRules,
        rulesByCategory,
        // Add more tax statistics as needed
      }
    });
  } catch (error) {
    console.error('Error fetching tax stats:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

/**
 * @route   GET /api/taxes/locations
 * @desc    Get available tax locations
 * @access  Private/Admin
 */
router.get('/locations', protect, admin, async (req, res) => {
  try {
    const locations = await TaxRule.aggregate([
      {
        $group: {
          _id: null,
          countries: { $addToSet: { $arrayElemAt: ['$countries', 0] } },
          states: { $addToSet: { $arrayElemAt: ['$states', 0] } },
          provinces: { $addToSet: { $arrayElemAt: ['$provinces', 0] } },
          cities: { $addToSet: { $arrayElemAt: ['$cities', 0] } }
        }
      }
    ]);

    res.json({
      success: true,
      locations: locations[0] || {
        countries: [],
        states: [],
        provinces: [],
        cities: []
      }
    });
  } catch (error) {
    console.error('Error fetching tax locations:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

module.exports = router;
