# Stripe API Keys
# Vite format
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_51QyG87FsgADt7MeasKUX0hR7wVWgYHIyxc58RnykgSLw1B4Hgkfna9TGmwxVSkuuaSoiIkUJocm6KPFy3V6tfpdK00uEEKF2Zk
VITE_STRIPE_SECRET_KEY=sk_test_51QyG87FsgADt7MeaXmHQyvQYGcjE71yuRGZv0xx0Le8WlZPY9O6rSTnrQOmURCeHc9udXjpF2r28voLB52z3Gv6700T4vpEd6w

# Legacy format (for backward compatibility)
REACT_APP_STRIPE_PUBLISHABLE_KEY=pk_test_51QyG87FsgADt7MeasKUX0hR7wVWgYHIyxc58RnykgSLw1B4Hgkfna9TGmwxVSkuuaSoiIkUJocm6KPFy3V6tfpdK00uEEKF2Zk
REACT_APP_STRIPE_SECRET_KEY=sk_test_51QyG87FsgADt7MeaXmHQyvQYGcjE71yuRGZv0xx0Le8WlZPY9O6rSTnrQOmURCeHc9udXjpF2r28voLB52z3Gv6700T4vpEd6w

# API URL
VITE_API_URL=http://localhost:4000

# Database (for server)
MONGODB_URI=your_mongodb_connection_string

# JWT Secret (for server)
JWT_SECRET=your_jwt_secret_key

# Vercel Blob Storage (for server)
BLOB_READ_WRITE_TOKEN=your_vercel_blob_token

# Environment
NODE_ENV=development

# Note: In a production environment, you should use your actual Stripe API keys
# and ensure they are properly secured. This is just a placeholder for development.
# Visit https://dashboard.stripe.com/apikeys to get your actual keys.
# For Vercel Blob, visit https://vercel.com/dashboard/stores to create a blob store and get your token.