const mongoose = require('mongoose');

const RefundSchema = new mongoose.Schema({
  order: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Order',
    required: true
  },
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  items: [{
    product: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Product',
      required: true
    },
    name: String,
    quantity: {
      type: Number,
      required: true,
      min: 1
    },
    price: {
      type: Number,
      required: true
    },
    reason: {
      type: String,
      required: true,
      enum: ['defective', 'wrong_item', 'not_as_described', 'damaged', 'changed_mind', 'other']
    }
  }],
  refundAmount: {
    type: Number,
    required: true,
    min: 0
  },
  refundMethod: {
    type: String,
    enum: ['original_payment', 'store_credit', 'bank_transfer'],
    default: 'original_payment'
  },
  status: {
    type: String,
    enum: ['pending', 'approved', 'rejected', 'processing', 'completed'],
    default: 'pending'
  },
  reason: {
    type: String,
    required: true
  },
  customerNotes: String,
  adminNotes: String,
  images: [String], // Evidence photos
  refundPolicy: {
    type: String,
    enum: ['full_refund', 'partial_refund', 'store_credit_only', 'exchange_only'],
    default: 'full_refund'
  },
  processedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  processedAt: Date,
  refundTransactionId: String, // Payment processor transaction ID
  estimatedProcessingDays: {
    type: Number,
    default: 5
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

RefundSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

module.exports = mongoose.model('Refund', RefundSchema);
