import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Star, ShoppingCart, Heart, Share2, Plus, Minus, ArrowLeft } from 'lucide-react';
import { Product } from '../types';
import { useCart } from '../context/CartContext';
import { useWishlist } from '../context/WishlistContext';
import { useToast } from '../context/ToastContext';
import { API_URL } from '../utils/env';
import ReviewSection from '../components/ReviewSection';
import { ProductCard } from '../components/ProductCard';

export function ProductDetailsPage() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { addItem } = useCart();
  const { addToWishlist, removeFromWishlist, isInWishlist } = useWishlist();
  const { showToast } = useToast();
  
  const [product, setProduct] = useState<Product | null>(null);
  const [quantity, setQuantity] = useState(1);
  const [selectedImage, setSelectedImage] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [suggestedProducts, setSuggestedProducts] = useState<Product[]>([]);
  const [loadingSuggestions, setLoadingSuggestions] = useState(false);
  const [pricingInfo, setPricingInfo] = useState<any>(null);

  // Fetch product details
  useEffect(() => {
    if (!id) return;

    const fetchProduct = async () => {
      setLoading(true);
      try {
        const response = await fetch(`${API_URL}/api/products/${id}?t=${Date.now()}`);
        
        if (response.ok) {
          const data = await response.json();
          const mappedProduct: Product = {
            id: data._id || data.id,
            name: data.name,
            price: data.price,
            originalPrice: data.originalPrice,
            image: data.image ? (data.image.startsWith('http') || data.image.startsWith('/uploads/') ? data.image : `${API_URL}${data.image}`) : '',
            images: data.images?.map((img: string) => img ? (img.startsWith('http') || img.startsWith('/uploads/') ? img : `${API_URL}${img}`) : '').filter(Boolean),
            description: data.description,
            category: data.category,
            rating: data.rating || 0,
            reviews: data.reviews || 0,
            inStock: data.inStock,
            featured: data.featured
          };
          setProduct(mappedProduct);

          // Set pricing information from server
          if (data.pricingInfo) {
            setPricingInfo(data.pricingInfo);
          } else {
            // Fallback pricing calculation
            setPricingInfo({
              originalPrice: data.originalPrice || data.price,
              effectivePrice: data.price,
              hasDiscount: !!(data.originalPrice && data.originalPrice > data.price),
              discountPercentage: data.originalPrice && data.originalPrice > data.price
                ? Math.round(((data.originalPrice - data.price) / data.originalPrice) * 100)
                : 0,
              savingsAmount: data.originalPrice && data.originalPrice > data.price
                ? data.originalPrice - data.price
                : 0,
              taxRate: data.taxRate || 0,
              taxIncluded: data.taxIncluded || false
            });
          }
        } else {
          setError('Product not found');
        }
      } catch (err) {
        console.error('Error fetching product:', err);
        setError('Failed to load product');
      } finally {
        setLoading(false);
      }
    };

    fetchProduct();
  }, [id]);

  // Listen for review updates and update product data immediately
  useEffect(() => {
    const handleReviewAdded = (event: CustomEvent) => {
      if (event.detail.productId === id && product) {
        console.log('🔄 ProductDetailsPage: Review added event received:', event.detail);

        // Update the product state immediately with the new rating and review count
        const updatedProduct = {
          ...product,
          rating: event.detail.newRating || product.rating,
          reviews: event.detail.reviewCount || (product.reviews + 1)
        };

        console.log('📊 ProductDetailsPage: Updating product state:', {
          oldRating: product.rating,
          newRating: updatedProduct.rating,
          oldReviews: product.reviews,
          newReviews: updatedProduct.reviews
        });

        setProduct(updatedProduct);
      }
    };

    const handleRatingUpdated = (event: CustomEvent) => {
      if (event.detail.productId === id && product) {
        console.log('📊 ProductDetailsPage: Rating updated event received:', event.detail);

        const updatedProduct = {
          ...product,
          rating: event.detail.rating || product.rating,
          reviews: event.detail.reviews || product.reviews
        };

        setProduct(updatedProduct);
      }
    };

    // Add event listeners
    document.addEventListener('productReviewAdded', handleReviewAdded as EventListener);
    document.addEventListener('productRatingUpdated', handleRatingUpdated as EventListener);

    return () => {
      document.removeEventListener('productReviewAdded', handleReviewAdded as EventListener);
      document.removeEventListener('productRatingUpdated', handleRatingUpdated as EventListener);
    };
  }, [id, product]);

  // Fetch suggested products
  useEffect(() => {
    if (!product) return;

    const fetchSuggestedProducts = async () => {
      setLoadingSuggestions(true);
      try {
        const response = await fetch(`${API_URL}/api/products?category=${encodeURIComponent(product.category)}&limit=8`);
        
        if (response.ok) {
          const data = await response.json();
          const suggestions = data.products
            .filter((p: any) => p._id !== product.id)
            .slice(0, 4)
            .map((p: any) => ({
              id: p._id || p.id,
              name: p.name,
              price: p.price,
              originalPrice: p.originalPrice,
              image: p.image ? (p.image.startsWith('http') || p.image.startsWith('/uploads/') ? p.image : `${API_URL}${p.image}`) : '',
              images: p.images?.map((img: string) => img ? (img.startsWith('http') || img.startsWith('/uploads/') ? img : `${API_URL}${img}`) : '').filter(Boolean),
              description: p.description,
              category: p.category,
              rating: p.rating || 0,
              reviews: p.reviews || 0,
              inStock: p.inStock,
              featured: p.featured
            }));
          
          setSuggestedProducts(suggestions);
        }
      } catch (error) {
        console.error('Error fetching suggested products:', error);
      } finally {
        setLoadingSuggestions(false);
      }
    };

    fetchSuggestedProducts();
  }, [product]);

  const handleAddToCart = () => {
    if (product) {
      addItem(product, quantity);
      showToast(`Added ${quantity} ${quantity === 1 ? 'item' : 'items'} to cart 🛒`, 'success');
    }
  };

  const handleWishlistToggle = () => {
    if (!product) return;
    
    if (isInWishlist(product.id)) {
      removeFromWishlist(product.id);
      showToast('Removed from wishlist', 'success');
    } else {
      addToWishlist(product);
      showToast('Added to wishlist ❤️', 'success');
    }
  };

  const handleShare = () => {
    if (navigator.share && product) {
      navigator.share({
        title: product.name,
        text: product.description,
        url: window.location.href,
      });
    } else {
      navigator.clipboard.writeText(window.location.href);
      showToast('Link copied to clipboard!', 'success');
    }
  };

  const handleViewSuggestedProduct = (suggestedProduct: Product) => {
    navigate(`/product/${suggestedProduct.id}`);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading product details...</p>
        </div>
      </div>
    );
  }

  if (error || !product) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Product Not Found</h1>
          <p className="text-gray-600 mb-6">{error || 'The product you are looking for does not exist.'}</p>
          <button
            onClick={() => navigate('/shop')}
            className="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors"
          >
            Back to Shop
          </button>
        </div>
      </div>
    );
  }

  const images = product.images && product.images.length > 0 ? product.images : [product.image];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Back Button */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <button
            onClick={() => navigate(-1)}
            className="flex items-center text-gray-600 hover:text-green-600 transition-colors"
          >
            <ArrowLeft className="h-5 w-5 mr-2" />
            Back
          </button>
        </div>
      </div>

      {/* Product Details */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 p-8">
            {/* Images */}
            <div className="space-y-4">
              <div className="aspect-square bg-gray-50 rounded-2xl overflow-hidden">
                <img
                  src={images[selectedImage]}
                  alt={product.name}
                  className="w-full h-full object-cover"
                />
              </div>
              
              {images.length > 1 && (
                <div className="flex space-x-2">
                  {images.map((image, index) => (
                    <button
                      key={index}
                      onClick={() => setSelectedImage(index)}
                      className={`w-16 h-16 rounded-lg overflow-hidden border-2 transition-colors ${
                        selectedImage === index ? 'border-green-600' : 'border-gray-200'
                      }`}
                    >
                      <img
                        src={image}
                        alt={`${product.name} ${index + 1}`}
                        className="w-full h-full object-cover"
                      />
                    </button>
                  ))}
                </div>
              )}
            </div>

            {/* Product Info */}
            <div className="space-y-6">
              <div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm text-gray-500 font-medium">{product.category}</span>
                  <div className="flex space-x-2">
                    <button
                      onClick={handleWishlistToggle}
                      className={`p-2 rounded-full transition-all duration-300 ${
                        isInWishlist(product.id)
                          ? 'bg-red-100 text-red-600 hover:bg-red-200'
                          : 'hover:bg-gray-100 text-gray-600 hover:text-red-500'
                      }`}
                    >
                      <Heart className={`h-5 w-5 ${isInWishlist(product.id) ? 'fill-current' : ''}`} />
                    </button>
                    <button
                      onClick={handleShare}
                      className="p-2 hover:bg-gray-100 rounded-full transition-colors text-gray-600 hover:text-green-600"
                    >
                      <Share2 className="h-5 w-5" />
                    </button>
                  </div>
                </div>
                
                <h1 className="text-3xl font-bold text-gray-900 mb-4">{product.name}</h1>
                
                <div className="flex items-center space-x-4 mb-4">
                  <div className="flex items-center space-x-1">
                    {[...Array(5)].map((_, i) => (
                      <Star
                        key={i}
                        className={`h-5 w-5 ${
                          i < Math.floor(product.rating)
                            ? 'text-yellow-400 fill-current'
                            : 'text-gray-300'
                        }`}
                      />
                    ))}
                  </div>
                  <span className="text-sm font-semibold text-green-700 bg-green-100 px-3 py-1 rounded-full">
                    {product.rating.toFixed(1)} ({product.reviews} reviews)
                  </span>
                </div>

                {/* Dynamic Pricing Display */}
                <div className="mb-6">
                  <div className="flex items-center space-x-3 mb-3">
                    <span className="text-4xl font-bold text-gray-900">
                      ${pricingInfo?.effectivePrice?.toFixed(2) || product.price.toFixed(2)}
                    </span>
                    {pricingInfo?.hasDiscount && pricingInfo.originalPrice > pricingInfo.effectivePrice && (
                      <>
                        <span className="text-2xl text-gray-500 line-through">
                          ${pricingInfo.originalPrice.toFixed(2)}
                        </span>
                        <span className="bg-red-100 text-red-800 px-3 py-1 rounded-full text-sm font-semibold animate-pulse">
                          {pricingInfo.discountPercentage}% OFF
                        </span>
                      </>
                    )}
                  </div>

                  {/* Savings Display */}
                  {pricingInfo?.hasDiscount && pricingInfo.savingsAmount > 0 && (
                    <div className="flex items-center space-x-2 mb-3">
                      <span className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-semibold">
                        💰 Save ${pricingInfo.savingsAmount.toFixed(2)}
                      </span>
                    </div>
                  )}

                  {/* Tax Information */}
                  {pricingInfo?.taxRate > 0 && (
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                      <div className="text-sm text-blue-800">
                        <div className="flex justify-between items-center mb-1">
                          <span>Product Price:</span>
                          <span className="font-semibold">${pricingInfo.effectivePrice.toFixed(2)}</span>
                        </div>
                        <div className="flex justify-between items-center mb-1">
                          <span>Tax ({pricingInfo.taxRate}%):</span>
                          <span className="font-semibold">
                            ${((pricingInfo.effectivePrice * pricingInfo.taxRate) / 100).toFixed(2)}
                          </span>
                        </div>
                        <div className="border-t border-blue-300 pt-1 mt-1">
                          <div className="flex justify-between items-center font-bold">
                            <span>Total (incl. tax):</span>
                            <span>${(pricingInfo.effectivePrice * (1 + pricingInfo.taxRate / 100)).toFixed(2)}</span>
                          </div>
                        </div>
                        <div className="text-xs text-blue-600 mt-1">
                          {pricingInfo.taxIncluded ? '* Tax is included in the displayed price' : '* Tax will be added at checkout'}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Description</h3>
                <p className="text-gray-600 leading-relaxed">{product.description}</p>
              </div>

              {/* Quantity and Add to Cart */}
              <div className="space-y-4">
                <div className="flex items-center space-x-4">
                  <span className="font-medium text-gray-900">Quantity:</span>
                  <div className="flex items-center border border-gray-300 rounded-lg">
                    <button
                      onClick={() => setQuantity(Math.max(1, quantity - 1))}
                      className="p-2 hover:bg-gray-100 transition-colors"
                    >
                      <Minus className="h-4 w-4" />
                    </button>
                    <span className="px-4 py-2 font-medium">{quantity}</span>
                    <button
                      onClick={() => setQuantity(quantity + 1)}
                      className="p-2 hover:bg-gray-100 transition-colors"
                    >
                      <Plus className="h-4 w-4" />
                    </button>
                  </div>
                </div>

                <div className="flex space-x-4">
                  <button
                    onClick={handleAddToCart}
                    className="flex-1 bg-green-600 text-white py-4 px-6 rounded-xl hover:bg-green-700 transition-all duration-300 font-semibold flex items-center justify-center space-x-2 shadow-lg hover:shadow-xl transform hover:scale-105"
                  >
                    <ShoppingCart className="h-5 w-5" />
                    <span>Add to Cart</span>
                  </button>

                  <button className="bg-green-700 text-white py-4 px-6 rounded-xl hover:bg-green-800 transition-all duration-300 font-semibold shadow-lg hover:shadow-xl transform hover:scale-105">
                    Buy Now
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Reviews Section */}
          <div className="border-t border-gray-200 p-8">
            <ReviewSection productId={product.id} />
          </div>
        </div>

        {/* Suggested Products */}
        {suggestedProducts.length > 0 && (
          <div className="mt-12">
            <div className="mb-8">
              <h2 className="text-3xl font-bold text-gray-900 mb-2">You Might Also Like</h2>
              <p className="text-gray-600">Similar products from the same category</p>
            </div>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
              {suggestedProducts.map((suggestedProduct) => (
                <ProductCard
                  key={suggestedProduct.id}
                  product={suggestedProduct}
                  onViewDetails={handleViewSuggestedProduct}
                />
              ))}
            </div>
          </div>
        )}

        {/* Loading State for Suggestions */}
        {loadingSuggestions && (
          <div className="mt-12">
            <div className="mb-8">
              <h2 className="text-3xl font-bold text-gray-900 mb-2">You Might Also Like</h2>
              <p className="text-gray-600">Loading suggestions...</p>
            </div>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
              {[1, 2, 3, 4].map((i) => (
                <div key={i} className="bg-gray-100 rounded-2xl h-80 animate-pulse"></div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
