const express = require('express');
const router = express.Router();
const Stats = require('../models/Stats');
const User = require('../models/User');
const Order = require('../models/Order');
const Product = require('../models/Product');
const { protect, admin } = require('../middleware/auth');

// Helper function to generate brand names from product data
const generateBrandFromProduct = (product) => {
  if (!product) return 'Unknown';

  // Category-based brand mapping
  const categoryBrands = {
    'Electronics': 'TechCorp',
    'Home & Office': 'HomeStyle',
    'Home & Kitchen': 'KitchenPro',
    'Photography': 'PhotoGear',
    'Lifestyle': 'LifeStyle',
    'Home & Decor': 'DecorPlus',
    'Fitness': 'FitGear',
    'Accessories': 'AccessPlus'
  };

  // Try to get brand from category
  if (product.category && categoryBrands[product.category]) {
    return categoryBrands[product.category];
  }

  // Try to extract brand from product name (first word if it looks like a brand)
  if (product.name) {
    const words = product.name.split(' ');
    const firstWord = words[0];

    // If first word is descriptive (like "Premium", "Smart", etc.), use category fallback
    const descriptiveWords = ['Premium', 'Smart', 'Professional', 'Artisan', 'Minimalist', 'Sustainable', 'Portable'];
    if (!descriptiveWords.includes(firstWord) && firstWord.length > 2) {
      return firstWord + 'Brand';
    }
  }

  return 'Generic Brand';
};

/**
 * @route   GET /api/stats
 * @desc    Get store statistics with date range support
 * @access  Private/Admin
 */
router.get('/', protect, admin, async (req, res) => {
  try {
    const { 
      startDate, 
      endDate, 
      period = 'all' // daily, weekly, monthly, custom, all
    } = req.query;

    // Calculate date range
    let dateFilter = {};
    const now = new Date();
    
    if (period === 'daily') {
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);
      dateFilter = { createdAt: { $gte: today, $lt: tomorrow } };
    } else if (period === 'weekly') {
      const weekAgo = new Date();
      weekAgo.setDate(weekAgo.getDate() - 7);
      dateFilter = { createdAt: { $gte: weekAgo } };
    } else if (period === 'monthly') {
      const monthAgo = new Date();
      monthAgo.setMonth(monthAgo.getMonth() - 1);
      dateFilter = { createdAt: { $gte: monthAgo } };
    } else if (period === 'custom' && startDate && endDate) {
      dateFilter = { 
        createdAt: { 
          $gte: new Date(startDate), 
          $lte: new Date(endDate) 
        } 
      };
    }

    // Get or create stats document
    let stats = await Stats.findOne({});
    if (!stats) {
      stats = new Stats();
      await stats.save();
    }

    // Get filtered orders for calculations
    const orderFilter = { 
      'paymentInfo.paymentStatus': 'succeeded',
      ...dateFilter
    };

    const orders = await Order.find(orderFilter).populate('items.product');
    
    // Calculate basic stats
    const totalUsers = await User.countDocuments();
    const totalOrders = await Order.countDocuments(orderFilter);
    const totalProducts = await Product.countDocuments();
    
    // Calculate revenue metrics
    const totalRevenue = orders.reduce((sum, order) => sum + order.total, 0);
    const grossSales = totalRevenue; // Before any deductions
    const netSales = totalRevenue; // After deductions (simplified for now)
    const averageOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;
    
    // Calculate refunds/returns (simplified - assuming cancelled orders)
    const refundedOrders = await Order.find({ 
      status: 'cancelled',
      ...dateFilter
    });
    const totalRefunds = refundedOrders.reduce((sum, order) => sum + order.total, 0);
    const refundCount = refundedOrders.length;

    // Get recent orders
    const recentOrders = await Order.find(orderFilter)
      .sort({ createdAt: -1 })
      .limit(10)
      .populate('user', 'firstName lastName email');

    // Get low stock and out of stock products
    const lowStockProducts = await Product.find({ inStock: false })
      .sort({ createdAt: -1 })
      .limit(10);

    // Calculate top and low performing products from filtered orders
    const productPerformance = new Map();
    
    for (const order of orders) {
      for (const item of order.items) {
        const productId = item.product._id.toString();
        
        if (productPerformance.has(productId)) {
          const data = productPerformance.get(productId);
          data.quantity += item.quantity;
          data.revenue += item.price * item.quantity;
        } else {
          productPerformance.set(productId, {
            product: item.product,
            quantity: item.quantity,
            revenue: item.price * item.quantity,
            productDetails: {
              name: item.product.name,
              image: item.product.image,
              price: item.product.price,
              category: item.product.category
            }
          });
        }
      }
    }
    
    const topSellingProducts = Array.from(productPerformance.values())
      .sort((a, b) => b.quantity - a.quantity)
      .slice(0, 10);
      
    const lowPerformingProducts = Array.from(productPerformance.values())
      .sort((a, b) => a.quantity - b.quantity)
      .slice(0, 10);

    // Calculate sales by category
    const categoryPerformance = new Map();
    
    for (const order of orders) {
      for (const item of order.items) {
        const category = item.product.category;
        const revenue = item.price * item.quantity;
        
        if (categoryPerformance.has(category)) {
          categoryPerformance.set(category, categoryPerformance.get(category) + revenue);
        } else {
          categoryPerformance.set(category, revenue);
        }
      }
    }
    
    const salesByCategory = Array.from(categoryPerformance.entries())
      .map(([category, sales]) => ({ category, sales }))
      .sort((a, b) => b.sales - a.sales);

    // Calculate sales by brand (if products have brand field)
    const brandPerformance = new Map();
    
    for (const order of orders) {
      for (const item of order.items) {
        // Better brand fallback logic
        let brand = item.product.brand;
        if (!brand || brand === 'Unknown' || brand.trim() === '') {
          // Generate brand based on category or product name
          brand = generateBrandFromProduct(item.product);
        }
        const revenue = item.price * item.quantity;
        
        if (brandPerformance.has(brand)) {
          brandPerformance.set(brand, brandPerformance.get(brand) + revenue);
        } else {
          brandPerformance.set(brand, revenue);
        }
      }
    }
    
    const salesByBrand = Array.from(brandPerformance.entries())
      .map(([brand, sales]) => ({ brand, sales }))
      .sort((a, b) => b.sales - a.sales);

    // Combine all stats
    const combinedStats = {
      // Basic metrics
      totalUsers,
      totalOrders,
      totalProducts,
      totalRevenue,
      grossSales,
      netSales,
      averageOrderValue,
      
      // Refunds/Returns
      totalRefunds,
      refundCount,
      refundRate: totalOrders > 0 ? (refundCount / totalOrders) * 100 : 0,
      totalTaxCollected: stats.totalTaxCollected || 0,
      
      // Product performance
      topSellingProducts,
      lowPerformingProducts,
      
      // Sales breakdowns
      salesByCategory,
      salesByBrand,
      
      // Time-based data (from stats collection)
      dailyRevenue: stats.dailyRevenue,
      monthlySales: stats.monthlySales,
      topCategories: stats.topCategories,
      salesByLocation: stats.salesByLocation || [],
      salesByProvince: stats.salesByProvince || [],
      taxByProvince: stats.taxByProvince || [],
      
      // Additional data
      recentOrders,
      lowStockProducts,
      
      // Metadata
      period,
      dateRange: {
        start: startDate || null,
        end: endDate || null
      }
    };

    res.json(combinedStats);
  } catch (error) {
    console.error('Error fetching stats:', error);
    res.status(500).json({ error: error.message });
  }
});

/**
 * @route   GET /api/stats/revenue
 * @desc    Get revenue statistics
 * @access  Private/Admin
 */
router.get('/revenue', protect, admin, async (req, res) => {
  try {
    const { period = 'daily', limit = 30 } = req.query;
    let stats = await Stats.findOne({});
    
    if (!stats) {
      stats = new Stats();
      await stats.save();
    }

    let revenueData;
    
    if (period === 'daily') {
      // Get daily revenue for the last X days
      revenueData = stats.dailyRevenue
        .sort((a, b) => new Date(b.date) - new Date(a.date))
        .slice(0, parseInt(limit));
    } else if (period === 'monthly') {
      // Get monthly revenue
      revenueData = stats.monthlySales
        .sort((a, b) => {
          if (a.year !== b.year) return b.year - a.year;
          return b.month - a.month;
        })
        .slice(0, parseInt(limit));
    }

    res.json(revenueData);
  } catch (error) {
    console.error('Error fetching revenue stats:', error);
    res.status(500).json({ error: error.message });
  }
});

/**
 * @route   GET /api/stats/products
 * @desc    Get product statistics
 * @access  Private/Admin
 */
router.get('/products', protect, admin, async (req, res) => {
  try {
    let stats = await Stats.findOne({});
    
    if (!stats) {
      stats = new Stats();
      await stats.save();
    }

    // Populate product details for top selling products
    const topSellingProducts = await Promise.all(
      stats.topSellingProducts.map(async (item) => {
        const product = await Product.findById(item.product);
        return {
          ...item.toObject(),
          productDetails: product ? {
            name: product.name,
            image: product.image,
            price: product.price
          } : null
        };
      })
    );

    res.json({
      topSellingProducts,
      topCategories: stats.topCategories
    });
  } catch (error) {
    console.error('Error fetching product stats:', error);
    res.status(500).json({ error: error.message });
  }
});

/**
 * @route   POST /api/stats/refresh
 * @desc    Refresh statistics (recalculate from orders)
 * @access  Private/Admin
 */
router.post('/refresh', protect, admin, async (req, res) => {
  try {
    // Create new stats object
    const stats = new Stats();
    
    // Count users
    stats.totalUsers = await User.countDocuments();
    
    // Get all orders
    const orders = await Order.find({ 'paymentInfo.paymentStatus': 'succeeded' });
    
    stats.totalOrders = orders.length;
    stats.totalRevenue = orders.reduce((sum, order) => sum + order.total, 0);
    
    // Calculate additional metrics
    stats.grossSales = orders.reduce((sum, order) => sum + order.subtotal + order.tax + order.shipping, 0);
    stats.netSales = orders.reduce((sum, order) => sum + order.subtotal, 0);
    stats.averageOrderValue = orders.length > 0 ? stats.totalRevenue / orders.length : 0;
    
    // Calculate refund metrics (assuming cancelled orders are refunds)
    const refundedOrders = orders.filter(order => order.status === 'cancelled' || order.status === 'refunded');
    stats.totalRefunds = refundedOrders.reduce((sum, order) => sum + order.total, 0);
    stats.refundCount = refundedOrders.length;
    stats.refundRate = orders.length > 0 ? (refundedOrders.length / orders.length) * 100 : 0;
    
    // Calculate tax metrics
    stats.totalTaxCollected = orders.reduce((sum, order) => sum + (order.tax || 0), 0);
    
    // Process sales by location (geographic insights)
    const locationSalesMap = new Map();
    const provinceSalesMap = new Map();
    const taxByProvinceMap = new Map();
    
    orders.forEach(order => {
      if (order.shippingAddress) {
        const city = order.shippingAddress.city || 'Unknown';
        const province = order.shippingAddress.state || order.shippingAddress.province || 'Unknown';
        const country = order.shippingAddress.country || 'Unknown';
        
        // Sales by city
        const locationKey = `${city}, ${province}, ${country}`;
        locationSalesMap.set(locationKey, (locationSalesMap.get(locationKey) || 0) + order.total);
        
        // Sales by province
        provinceSalesMap.set(province, (provinceSalesMap.get(province) || 0) + order.total);
        
        // Tax by province
        taxByProvinceMap.set(province, (taxByProvinceMap.get(province) || 0) + (order.tax || 0));
      }
    });
    
    stats.salesByLocation = Array.from(locationSalesMap).map(([location, sales]) => ({
      location,
      sales
    })).sort((a, b) => b.sales - a.sales).slice(0, 20);
    
    stats.salesByProvince = Array.from(provinceSalesMap).map(([province, sales]) => ({
      province,
      sales
    })).sort((a, b) => b.sales - a.sales);
    
    stats.taxByProvince = Array.from(taxByProvinceMap).map(([province, tax]) => ({
      province,
      tax
    })).sort((a, b) => b.tax - a.tax);
    
    // Calculate sales by brand
    const brandSalesMap = new Map();
    
    for (const order of orders) {
      for (const item of order.items) {
        const product = await Product.findById(item.product);
        if (product) {
          let brand = product.brand;
          if (!brand || brand === 'Unknown' || brand.trim() === '') {
            brand = generateBrandFromProduct(product);
          }
          brandSalesMap.set(brand, (brandSalesMap.get(brand) || 0) + (item.price * item.quantity));
        }
      }
    }
    
    stats.salesByBrand = Array.from(brandSalesMap).map(([brand, sales]) => ({
      brand,
      sales
    })).sort((a, b) => b.sales - a.sales).slice(0, 10);
    
    // Process daily revenue
    const dailyRevenueMap = new Map();
    
    orders.forEach(order => {
      const orderDate = new Date(order.createdAt);
      orderDate.setHours(0, 0, 0, 0);
      const dateString = orderDate.toISOString();
      
      if (dailyRevenueMap.has(dateString)) {
        dailyRevenueMap.set(dateString, dailyRevenueMap.get(dateString) + order.total);
      } else {
        dailyRevenueMap.set(dateString, order.total);
      }
    });
    
    stats.dailyRevenue = Array.from(dailyRevenueMap).map(([date, amount]) => ({
      date: new Date(date),
      amount
    }));
    
    // Process monthly sales
    const monthlySalesMap = new Map();
    
    orders.forEach(order => {
      const orderDate = new Date(order.createdAt);
      const month = orderDate.getMonth();
      const year = orderDate.getFullYear();
      const key = `${year}-${month}`;
      
      if (monthlySalesMap.has(key)) {
        const data = monthlySalesMap.get(key);
        data.amount += order.total;
        data.orders += 1;
      } else {
        monthlySalesMap.set(key, {
          month,
          year,
          amount: order.total,
          orders: 1
        });
      }
    });
    
    stats.monthlySales = Array.from(monthlySalesMap.values());
    
    // Process top selling products
    const productSalesMap = new Map();
    
    for (const order of orders) {
      for (const item of order.items) {
        const productId = item.product.toString();
        
        if (productSalesMap.has(productId)) {
          const data = productSalesMap.get(productId);
          data.quantity += item.quantity;
          data.revenue += item.price * item.quantity;
        } else {
          productSalesMap.set(productId, {
            product: item.product,
            quantity: item.quantity,
            revenue: item.price * item.quantity
          });
        }
      }
    }
    
    stats.topSellingProducts = Array.from(productSalesMap.values())
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, 10);
    
    // Calculate low performing products
    stats.lowPerformingProducts = Array.from(productSalesMap.values())
      .sort((a, b) => a.revenue - b.revenue)
      .slice(0, 10);
    
    // Process top categories
    const categorySalesMap = new Map();
    
    for (const order of orders) {
      for (const item of order.items) {
        // Get product to find its category
        const product = await Product.findById(item.product);
        if (product) {
          const category = product.category;
          
          if (categorySalesMap.has(category)) {
            categorySalesMap.set(
              category, 
              categorySalesMap.get(category) + (item.price * item.quantity)
            );
          } else {
            categorySalesMap.set(category, item.price * item.quantity);
          }
        }
      }
    }
    
    stats.topCategories = Array.from(categorySalesMap).map(([category, sales]) => ({
      category,
      sales
    })).sort((a, b) => b.sales - a.sales).slice(0, 10);
    
    // Delete old stats and save new one
    await Stats.deleteMany({});
    await stats.save();
    
    res.json({ message: 'Statistics refreshed successfully', stats });
  } catch (error) {
    console.error('Error refreshing stats:', error);
    res.status(500).json({ error: error.message });
  }
});

module.exports = router;