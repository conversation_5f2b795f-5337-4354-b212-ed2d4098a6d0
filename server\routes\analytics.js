const express = require('express');
const router = express.Router();
const Stats = require('../models/Stats');
const User = require('../models/User');
const Order = require('../models/Order');
const Product = require('../models/Product');
const { protect, admin } = require('../middleware/auth');
const json2csv = require('json2csv').parse;
const ExcelJS = require('exceljs');
const nodemailer = require('nodemailer');
const cron = require('node-cron');

/**
 * @route   GET /api/analytics/category-comparison
 * @desc    Compare categories side-by-side with detailed metrics
 * @access  Private/Admin
 */
router.get('/category-comparison', protect, admin, async (req, res) => {
  try {
    const { startDate, endDate, categories } = req.query;
    
    let dateFilter = {};
    if (startDate && endDate) {
      dateFilter = {
        createdAt: {
          $gte: new Date(startDate),
          $lte: new Date(endDate)
        }
      };
    }

    const orders = await Order.find({
      'paymentInfo.paymentStatus': 'succeeded',
      ...dateFilter
    }).populate('items.product');

    const categoryMetrics = new Map();
    const totalVisitors = await User.countDocuments(dateFilter); // Simplified visitor count

    // Calculate metrics for each category
    for (const order of orders) {
      for (const item of order.items) {
        const category = item.product.category;
        const revenue = item.price * item.quantity;
        const cost = item.product.originalPrice || item.price * 0.7; // Simplified cost calculation
        const profit = revenue - (cost * item.quantity);
        
        if (!categoryMetrics.has(category)) {
          categoryMetrics.set(category, {
            category,
            totalSales: 0,
            unitsSold: 0,
            revenue: 0,
            profit: 0,
            orders: new Set(),
            products: new Set()
          });
        }
        
        const metrics = categoryMetrics.get(category);
        metrics.totalSales += revenue;
        metrics.unitsSold += item.quantity;
        metrics.revenue += revenue;
        metrics.profit += profit;
        metrics.orders.add(order._id.toString());
        metrics.products.add(item.product._id.toString());
      }
    }

    // Convert to array and calculate additional metrics
    const comparison = Array.from(categoryMetrics.values()).map(metrics => {
      const uniqueOrders = metrics.orders.size;
      const conversionRate = totalVisitors > 0 ? (uniqueOrders / totalVisitors) * 100 : 0;
      const profitMargin = metrics.revenue > 0 ? (metrics.profit / metrics.revenue) * 100 : 0;
      const avgOrderValue = uniqueOrders > 0 ? metrics.revenue / uniqueOrders : 0;
      
      return {
        category: metrics.category,
        totalSales: metrics.totalSales,
        unitsSold: metrics.unitsSold,
        revenue: metrics.revenue,
        profit: metrics.profit,
        profitMargin,
        orders: uniqueOrders,
        conversionRate,
        avgOrderValue,
        productsCount: metrics.products.size
      };
    }).sort((a, b) => b.revenue - a.revenue);

    res.json({
      success: true,
      data: comparison,
      summary: {
        totalCategories: comparison.length,
        totalRevenue: comparison.reduce((sum, cat) => sum + cat.revenue, 0),
        totalProfit: comparison.reduce((sum, cat) => sum + cat.profit, 0),
        avgConversionRate: comparison.reduce((sum, cat) => sum + cat.conversionRate, 0) / comparison.length
      }
    });
  } catch (error) {
    console.error('Category comparison error:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

/**
 * @route   GET /api/analytics/product-performance-over-time
 * @desc    Track product performance over different time periods
 * @access  Private/Admin
 */
router.get('/product-performance-over-time', protect, admin, async (req, res) => {
  try {
    const { productId, period = 'monthly', startDate, endDate } = req.query;
    
    if (!productId) {
      return res.status(400).json({ success: false, message: 'Product ID is required' });
    }

    let dateFilter = {};
    if (startDate && endDate) {
      dateFilter = {
        createdAt: {
          $gte: new Date(startDate),
          $lte: new Date(endDate)
        }
      };
    }

    const orders = await Order.find({
      'paymentInfo.paymentStatus': 'succeeded',
      'items.product': productId,
      ...dateFilter
    }).populate('items.product');

    const product = await Product.findById(productId);
    if (!product) {
      return res.status(404).json({ success: false, message: 'Product not found' });
    }

    // Group data by time period
    const performanceData = new Map();
    
    for (const order of orders) {
      const orderDate = new Date(order.createdAt);
      let periodKey;
      
      if (period === 'daily') {
        periodKey = orderDate.toISOString().split('T')[0];
      } else if (period === 'weekly') {
        const weekStart = new Date(orderDate);
        weekStart.setDate(orderDate.getDate() - orderDate.getDay());
        periodKey = weekStart.toISOString().split('T')[0];
      } else { // monthly
        periodKey = `${orderDate.getFullYear()}-${String(orderDate.getMonth() + 1).padStart(2, '0')}`;
      }
      
      if (!performanceData.has(periodKey)) {
        performanceData.set(periodKey, {
          period: periodKey,
          sales: 0,
          units: 0,
          revenue: 0,
          orders: 0
        });
      }
      
      const data = performanceData.get(periodKey);
      
      for (const item of order.items) {
        if (item.product._id.toString() === productId) {
          data.sales += item.price * item.quantity;
          data.units += item.quantity;
          data.revenue += item.price * item.quantity;
          data.orders += 1;
        }
      }
    }

    const timeSeriesData = Array.from(performanceData.values())
      .sort((a, b) => new Date(a.period) - new Date(b.period));

    res.json({
      success: true,
      product: {
        id: product._id,
        name: product.name,
        category: product.category,
        brand: product.brand
      },
      period,
      data: timeSeriesData,
      summary: {
        totalRevenue: timeSeriesData.reduce((sum, item) => sum + item.revenue, 0),
        totalUnits: timeSeriesData.reduce((sum, item) => sum + item.units, 0),
        totalOrders: timeSeriesData.reduce((sum, item) => sum + item.orders, 0),
        avgRevenuePerPeriod: timeSeriesData.length > 0 ? timeSeriesData.reduce((sum, item) => sum + item.revenue, 0) / timeSeriesData.length : 0
      }
    });
  } catch (error) {
    console.error('Product performance over time error:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

/**
 * @route   GET /api/analytics/export
 * @desc    Export analytics data in CSV or Excel format
 * @access  Private/Admin
 */
router.get('/export', protect, admin, async (req, res) => {
  try {
    const { format = 'csv', type = 'sales', startDate, endDate } = req.query;
    
    let dateFilter = {};
    if (startDate && endDate) {
      dateFilter = {
        createdAt: {
          $gte: new Date(startDate),
          $lte: new Date(endDate)
        }
      };
    }

    let data = [];
    let filename = '';
    
    if (type === 'sales') {
      const orders = await Order.find({
        'paymentInfo.paymentStatus': 'succeeded',
        ...dateFilter
      }).populate('user', 'firstName lastName email')
        .populate('items.product', 'name category brand price');
      
      data = orders.map(order => ({
        orderId: order._id,
        customerName: `${order.user?.firstName || ''} ${order.user?.lastName || ''}`,
        customerEmail: order.user?.email || '',
        total: order.total,
        tax: order.tax || 0,
        shipping: order.shipping || 0,
        status: order.status,
        paymentMethod: order.paymentInfo?.paymentMethod || '',
        shippingAddress: `${order.shippingAddress?.city || ''}, ${order.shippingAddress?.province || ''}`,
        createdAt: order.createdAt,
        itemsCount: order.items.length
      }));
      filename = `sales-report-${new Date().toISOString().split('T')[0]}`;
    } else if (type === 'products') {
      const products = await Product.find({});
      data = products.map(product => ({
        productId: product._id,
        name: product.name,
        category: product.category,
        brand: product.brand || 'Unknown',
        price: product.price,
        originalPrice: product.originalPrice,
        inStock: product.inStock,
        createdAt: product.createdAt
      }));
      filename = `products-report-${new Date().toISOString().split('T')[0]}`;
    } else if (type === 'customers') {
      const users = await User.find(dateFilter);
      data = users.map(user => ({
        userId: user._id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        role: user.role,
        createdAt: user.createdAt
      }));
      filename = `customers-report-${new Date().toISOString().split('T')[0]}`;
    }

    if (format === 'csv') {
      const csv = json2csv(data);
      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', `attachment; filename="${filename}.csv"`);
      res.send(csv);
    } else if (format === 'excel') {
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('Report');
      
      if (data.length > 0) {
        const headers = Object.keys(data[0]);
        worksheet.addRow(headers);
        
        data.forEach(row => {
          worksheet.addRow(Object.values(row));
        });
        
        // Style the header row
        worksheet.getRow(1).font = { bold: true };
        worksheet.getRow(1).fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'FFE0E0E0' }
        };
      }
      
      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      res.setHeader('Content-Disposition', `attachment; filename="${filename}.xlsx"`);
      
      await workbook.xlsx.write(res);
      res.end();
    }
  } catch (error) {
    console.error('Export error:', error);
    res.status(500).json({ success: false, message: 'Export failed' });
  }
});

/**
 * @route   POST /api/analytics/custom-report
 * @desc    Generate custom reports with filters
 * @access  Private/Admin
 */
router.post('/custom-report', protect, admin, async (req, res) => {
  try {
    const {
      startDate,
      endDate,
      productTypes = [],
      regions = [],
      provinces = [],
      categories = [],
      brands = [],
      metrics = ['revenue', 'orders', 'units']
    } = req.body;

    let dateFilter = {};
    if (startDate && endDate) {
      dateFilter = {
        createdAt: {
          $gte: new Date(startDate),
          $lte: new Date(endDate)
        }
      };
    }

    // Build product filter
    let productFilter = {};
    if (categories.length > 0) {
      productFilter.category = { $in: categories };
    }
    if (brands.length > 0) {
      productFilter.brand = { $in: brands };
    }

    // Get filtered products
    const products = await Product.find(productFilter);
    const productIds = products.map(p => p._id);

    // Build order filter
    let orderFilter = {
      'paymentInfo.paymentStatus': 'succeeded',
      ...dateFilter
    };
    
    if (productIds.length > 0) {
      orderFilter['items.product'] = { $in: productIds };
    }
    
    if (regions.length > 0) {
      orderFilter['shippingAddress.city'] = { $in: regions };
    }
    
    if (provinces.length > 0) {
      orderFilter['shippingAddress.province'] = { $in: provinces };
    }

    const orders = await Order.find(orderFilter)
      .populate('items.product')
      .populate('user', 'firstName lastName email');

    // Calculate metrics
    const report = {
      filters: {
        dateRange: { startDate, endDate },
        productTypes,
        regions,
        provinces,
        categories,
        brands
      },
      summary: {
        totalOrders: orders.length,
        totalRevenue: 0,
        totalUnits: 0,
        avgOrderValue: 0,
        uniqueCustomers: new Set()
      },
      breakdown: {
        byCategory: new Map(),
        byBrand: new Map(),
        byRegion: new Map(),
        byProvince: new Map(),
        byMonth: new Map()
      },
      topProducts: [],
      topCustomers: []
    };

    // Process orders
    for (const order of orders) {
      report.summary.totalRevenue += order.total;
      report.summary.uniqueCustomers.add(order.user?._id?.toString());
      
      const month = new Date(order.createdAt).toISOString().substring(0, 7);
      if (!report.breakdown.byMonth.has(month)) {
        report.breakdown.byMonth.set(month, { revenue: 0, orders: 0, units: 0 });
      }
      const monthData = report.breakdown.byMonth.get(month);
      monthData.revenue += order.total;
      monthData.orders += 1;
      
      // Process items
      for (const item of order.items) {
        report.summary.totalUnits += item.quantity;
        monthData.units += item.quantity;
        
        const category = item.product.category;
        const brand = item.product.brand || 'Unknown';
        const region = order.shippingAddress?.city || 'Unknown';
        const province = order.shippingAddress?.province || 'Unknown';
        
        // Category breakdown
        if (!report.breakdown.byCategory.has(category)) {
          report.breakdown.byCategory.set(category, { revenue: 0, units: 0, orders: 0 });
        }
        const catData = report.breakdown.byCategory.get(category);
        catData.revenue += item.price * item.quantity;
        catData.units += item.quantity;
        catData.orders += 1;
        
        // Brand breakdown
        if (!report.breakdown.byBrand.has(brand)) {
          report.breakdown.byBrand.set(brand, { revenue: 0, units: 0, orders: 0 });
        }
        const brandData = report.breakdown.byBrand.get(brand);
        brandData.revenue += item.price * item.quantity;
        brandData.units += item.quantity;
        brandData.orders += 1;
        
        // Region breakdown
        if (!report.breakdown.byRegion.has(region)) {
          report.breakdown.byRegion.set(region, { revenue: 0, units: 0, orders: 0 });
        }
        const regionData = report.breakdown.byRegion.get(region);
        regionData.revenue += item.price * item.quantity;
        regionData.units += item.quantity;
        regionData.orders += 1;
        
        // Province breakdown
        if (!report.breakdown.byProvince.has(province)) {
          report.breakdown.byProvince.set(province, { revenue: 0, units: 0, orders: 0 });
        }
        const provData = report.breakdown.byProvince.get(province);
        provData.revenue += item.price * item.quantity;
        provData.units += item.quantity;
        provData.orders += 1;
      }
    }

    // Calculate averages
    report.summary.avgOrderValue = report.summary.totalOrders > 0 ? 
      report.summary.totalRevenue / report.summary.totalOrders : 0;
    report.summary.uniqueCustomers = report.summary.uniqueCustomers.size;

    // Convert Maps to Arrays
    report.breakdown.byCategory = Array.from(report.breakdown.byCategory.entries())
      .map(([category, data]) => ({ category, ...data }))
      .sort((a, b) => b.revenue - a.revenue);
    
    report.breakdown.byBrand = Array.from(report.breakdown.byBrand.entries())
      .map(([brand, data]) => ({ brand, ...data }))
      .sort((a, b) => b.revenue - a.revenue);
    
    report.breakdown.byRegion = Array.from(report.breakdown.byRegion.entries())
      .map(([region, data]) => ({ region, ...data }))
      .sort((a, b) => b.revenue - a.revenue);
    
    report.breakdown.byProvince = Array.from(report.breakdown.byProvince.entries())
      .map(([province, data]) => ({ province, ...data }))
      .sort((a, b) => b.revenue - a.revenue);
    
    report.breakdown.byMonth = Array.from(report.breakdown.byMonth.entries())
      .map(([month, data]) => ({ month, ...data }))
      .sort((a, b) => new Date(a.month) - new Date(b.month));

    res.json({
      success: true,
      report,
      generatedAt: new Date().toISOString()
    });
  } catch (error) {
    console.error('Custom report error:', error);
    res.status(500).json({ success: false, message: 'Report generation failed' });
  }
});

module.exports = router;