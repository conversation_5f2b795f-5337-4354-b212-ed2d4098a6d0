# Quick Fix for Your Deployment Issue

## The Problem
Your frontend is deployed but can't connect to the backend because:
1. Frontend is trying to connect to `localhost:4000` 
2. Backend is not deployed/running in production

## Immediate Solution

### Option 1: Quick Fix (Recommended)
1. **Go to your Vercel dashboard** (https://vercel.com/dashboard)
2. **Find your project** and click on it
3. **Go to Settings → Environment Variables**
4. **Add these environment variables:**

```
Name: VITE_STRIPE_PUBLISHABLE_KEY
Value: pk_test_51QyG87FsgADt7MeasKUX0hR7wVWgYHIyxc58RnykgSLw1B4Hgkfna9TGmwxVSkuuaSoiIkUJocm6KPFy3V6tfpdK00uEEKF2Zk

Name: VITE_STRIPE_SECRET_KEY  
Value: sk_test_51QyG87FsgADt7MeaXmHQyvQYGcjE71yuRGZv0xx0Le8WlZPY9O6rSTnrQOmURCeHc9udXjpF2r28voLB52z3Gv6700T4vpEd6w

Name: MONGODB_URI
Value: mongodb://sulta4567:<EMAIL>:27017,cluster0-shard-00-01.c9q8b.mongodb.net:27017,cluster0-shard-00-02.c9q8b.mongodb.net:27017/jaisalgoonline?replicaSet=atlas-v49dz3-shard-0&ssl=true&authSource=admin

Name: JWT_SECRET
Value: your_jwt_secret_key_here

Name: BLOB_READ_WRITE_TOKEN
Value: a94wAAm68ve0dsabgkUF00QL
```

5. **Redeploy your project:**
   - Go to Deployments tab
   - Click the three dots on the latest deployment
   - Click "Redeploy"

### Option 2: Command Line Fix
If you have Vercel CLI installed:

```bash
# Install Vercel CLI if you haven't
npm i -g vercel

# Set environment variables
vercel env add VITE_STRIPE_PUBLISHABLE_KEY
vercel env add VITE_STRIPE_SECRET_KEY  
vercel env add MONGODB_URI
vercel env add JWT_SECRET
vercel env add BLOB_READ_WRITE_TOKEN

# Redeploy
vercel --prod
```

## What This Does
- Deploys your backend as Vercel serverless functions
- Makes your API available at `https://your-domain.vercel.app/api/*`
- Frontend will automatically connect to the right API URL

## Test After Deployment
1. Visit your deployed site
2. Check browser console - the connection errors should be gone
3. Try using the app features (browsing products, etc.)

## If Still Not Working
1. Check Vercel function logs in your dashboard
2. Make sure MongoDB allows connections from Vercel IPs
3. Verify all environment variables are set correctly

Your app should work after these steps! 🚀
