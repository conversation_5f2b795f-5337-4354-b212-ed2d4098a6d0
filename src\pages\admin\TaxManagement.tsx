import React, { useState, useEffect } from 'react';
import { Plus, Edit, Trash2, RefreshCw, Receipt, Percent, DollarSign, ArrowLeft } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { API_URL } from '../../utils/env';

interface TaxRule {
  _id: string;
  name: string;
  description?: string;
  type: 'percentage' | 'fixed_amount';
  rate: number;
  countries: string[];
  states: string[];
  provinces: string[];
  cities: string[];
  applicableCategories: string[];
  minimumOrderAmount: number;
  maximumOrderAmount?: number;
  isActive: boolean;
  priority: number;
  reportingCategory: string;
  createdAt: string;
}

const TaxManagement: React.FC = () => {
  const navigate = useNavigate();
  const [taxRules, setTaxRules] = useState<TaxRule[]>([]);
  const [loading, setLoading] = useState(true);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingTaxRule, setEditingTaxRule] = useState<TaxRule | null>(null);
  const [statusFilter, setStatusFilter] = useState('all');

  const token = localStorage.getItem('token');

  useEffect(() => {
    fetchTaxRules();
  }, [statusFilter]);

  const fetchTaxRules = async () => {
    try {
      setLoading(true);
      const response = await fetch(`${API_URL}/api/taxes?status=${statusFilter}`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      if (!response.ok) {
        throw new Error('Failed to fetch tax rules');
      }
      
      const data = await response.json();
      setTaxRules(data.taxRules || []);
    } catch (error) {
      console.error('Error fetching tax rules:', error);
      alert('Failed to load tax rules');
    } finally {
      setLoading(false);
    }
  };

  const deleteTaxRule = async (id: string) => {
    if (!confirm('Are you sure you want to delete this tax rule?')) return;
    
    try {
      const response = await fetch(`${API_URL}/api/taxes/${id}`, {
        method: 'DELETE',
        headers: { Authorization: `Bearer ${token}` }
      });
      
      if (!response.ok) {
        throw new Error('Failed to delete tax rule');
      }
      
      alert('Tax rule deleted successfully');
      fetchTaxRules();
    } catch (error) {
      console.error('Error deleting tax rule:', error);
      alert('Failed to delete tax rule');
    }
  };

  const getTypeIcon = (type: string) => {
    return type === 'percentage' ? <Percent className="h-4 w-4" /> : <DollarSign className="h-4 w-4" />;
  };

  const getStatusColor = (isActive: boolean) => {
    return isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800';
  };

  return (
    <div className="p-6 bg-gradient-to-br from-green-50 to-emerald-50 min-h-screen">
      {/* Back Button */}
      <button
        onClick={() => navigate('/admin')}
        className="flex items-center text-green-600 hover:text-green-700 mb-6 transition-colors"
      >
        <ArrowLeft className="h-5 w-5 mr-2" />
        Back to Admin Dashboard
      </button>

      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center">
            <span className="w-10 h-10 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center mr-4 shadow-lg">
              <Receipt className="h-6 w-6 text-white" />
            </span>
            Tax Rules Management
          </h1>
          <p className="text-gray-600 mt-2">Manage tax rules and rates for different locations and categories</p>
        </div>
        <div className="flex space-x-4">
          <button
            onClick={fetchTaxRules}
            disabled={loading}
            className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 shadow-md"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </button>
          <button
            onClick={() => setShowCreateModal(true)}
            className="flex items-center px-4 py-2 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700 transition-colors shadow-md"
          >
            <Plus className="h-4 w-4 mr-2" />
            Create Tax Rule
          </button>
        </div>
      </div>

      {/* Filters - Green Theme */}
      <div className="bg-white rounded-xl shadow-md border border-green-200 p-6 mb-6">
        <div className="flex flex-wrap gap-4">
          <div>
            <label className="block text-sm font-medium text-green-800 mb-2">Status Filter</label>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-4 py-3 border border-green-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200 bg-white hover:border-green-400"
            >
              <option value="all">All Tax Rules</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>
          </div>
        </div>
      </div>

      {/* Tax Rules List - Green Theme */}
      <div className="bg-white rounded-xl shadow-md border border-green-200 overflow-hidden">
        {loading ? (
          <div className="p-8 text-center">
            <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-green-500" />
            <p className="text-green-600">Loading tax rules...</p>
          </div>
        ) : taxRules.length === 0 ? (
          <div className="p-8 text-center">
            <Receipt className="h-12 w-12 mx-auto mb-4 text-green-400" />
            <p className="text-green-600">No tax rules found</p>
            <button
              onClick={() => setShowCreateModal(true)}
              className="mt-4 text-green-600 hover:text-green-800 font-medium"
            >
              Create your first tax rule
            </button>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-green-50 border-b border-green-200">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Tax Rule
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Type & Rate
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Location
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Priority
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {taxRules.map((taxRule) => (
                  <tr key={taxRule._id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {taxRule.name}
                        </div>
                        {taxRule.description && (
                          <div className="text-xs text-gray-400 mt-1">
                            {taxRule.description}
                          </div>
                        )}
                        <div className="text-xs text-gray-500 mt-1">
                          {taxRule.reportingCategory.toUpperCase()}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        {getTypeIcon(taxRule.type)}
                        <div className="ml-2">
                          <div className="text-sm font-medium text-gray-900">
                            {taxRule.type === 'percentage' ? `${taxRule.rate}%` : `$${taxRule.rate}`}
                          </div>
                          <div className="text-xs text-gray-500">
                            {taxRule.type === 'percentage' ? 'Percentage' : 'Fixed Amount'}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {taxRule.countries.length > 0 && (
                          <div>Countries: {taxRule.countries.join(', ')}</div>
                        )}
                        {taxRule.provinces.length > 0 && (
                          <div>Provinces: {taxRule.provinces.join(', ')}</div>
                        )}
                        {taxRule.states.length > 0 && (
                          <div>States: {taxRule.states.join(', ')}</div>
                        )}
                        {taxRule.countries.length === 0 && taxRule.provinces.length === 0 && taxRule.states.length === 0 && (
                          <span className="text-gray-500">All locations</span>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">
                        {taxRule.priority}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(taxRule.isActive)}`}>
                        {taxRule.isActive ? 'Active' : 'Inactive'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2">
                        <button
                          onClick={() => setEditingTaxRule(taxRule)}
                          className="text-blue-600 hover:text-blue-900"
                        >
                          <Edit className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => deleteTaxRule(taxRule._id)}
                          className="text-red-600 hover:text-red-900"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Create/Edit Modal */}
      {(showCreateModal || editingTaxRule) && (
        <TaxRuleModal
          taxRule={editingTaxRule}
          onClose={() => {
            setShowCreateModal(false);
            setEditingTaxRule(null);
          }}
          onSave={() => {
            fetchTaxRules();
            setShowCreateModal(false);
            setEditingTaxRule(null);
          }}
        />
      )}
    </div>
  );
};

// Tax Rule Modal Component
interface TaxRuleModalProps {
  taxRule: TaxRule | null;
  onClose: () => void;
  onSave: () => void;
}

const TaxRuleModal: React.FC<TaxRuleModalProps> = ({ taxRule, onClose, onSave }) => {
  const [formData, setFormData] = useState({
    name: taxRule?.name || '',
    description: taxRule?.description || '',
    type: taxRule?.type || 'percentage',
    rate: taxRule?.rate || 0,
    countries: taxRule?.countries.join(', ') || '',
    states: taxRule?.states.join(', ') || '',
    provinces: taxRule?.provinces.join(', ') || '',
    cities: taxRule?.cities.join(', ') || '',
    applicableCategories: taxRule?.applicableCategories.join(', ') || '',
    minimumOrderAmount: taxRule?.minimumOrderAmount || 0,
    maximumOrderAmount: taxRule?.maximumOrderAmount || '',
    priority: taxRule?.priority || 0,
    reportingCategory: taxRule?.reportingCategory || 'sales_tax',
    isActive: taxRule?.isActive ?? true
  });

  const [saving, setSaving] = useState(false);
  const token = localStorage.getItem('token');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSaving(true);

    try {
      const url = taxRule 
        ? `${API_URL}/api/taxes/${taxRule._id}`
        : `${API_URL}/api/taxes`;
      
      const method = taxRule ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`
        },
        body: JSON.stringify({
          ...formData,
          countries: formData.countries.split(',').map(s => s.trim()).filter(Boolean),
          states: formData.states.split(',').map(s => s.trim()).filter(Boolean),
          provinces: formData.provinces.split(',').map(s => s.trim()).filter(Boolean),
          cities: formData.cities.split(',').map(s => s.trim()).filter(Boolean),
          applicableCategories: formData.applicableCategories.split(',').map(s => s.trim()).filter(Boolean),
          maximumOrderAmount: formData.maximumOrderAmount || undefined
        })
      });

      if (!response.ok) {
        throw new Error('Failed to save tax rule');
      }

      const data = await response.json();
      alert(data.message);
      onSave();
    } catch (error) {
      console.error('Error saving tax rule:', error);
      alert('Failed to save tax rule');
    } finally {
      setSaving(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-xl font-bold text-gray-900">
            {taxRule ? 'Edit Tax Rule' : 'Create New Tax Rule'}
          </h2>
        </div>
        
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Tax Rule Name *
              </label>
              <input
                type="text"
                required
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="e.g., GST Canada"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Reporting Category *
              </label>
              <select
                required
                value={formData.reportingCategory}
                onChange={(e) => setFormData({ ...formData, reportingCategory: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="sales_tax">Sales Tax</option>
                <option value="vat">VAT</option>
                <option value="gst">GST</option>
                <option value="excise">Excise Tax</option>
                <option value="customs">Customs Duty</option>
                <option value="other">Other</option>
              </select>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Description
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              rows={2}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Optional description for this tax rule"
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Tax Type *
              </label>
              <select
                required
                value={formData.type}
                onChange={(e) => setFormData({ ...formData, type: e.target.value as any })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="percentage">Percentage</option>
                <option value="fixed_amount">Fixed Amount</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {formData.type === 'percentage' ? 'Rate (%)' : 'Amount ($)'}
              </label>
              <input
                type="number"
                required
                min="0"
                step={formData.type === 'percentage' ? '0.01' : '0.01'}
                max={formData.type === 'percentage' ? '100' : undefined}
                value={formData.rate}
                onChange={(e) => setFormData({ ...formData, rate: parseFloat(e.target.value) || 0 })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Priority
              </label>
              <input
                type="number"
                min="0"
                value={formData.priority}
                onChange={(e) => setFormData({ ...formData, priority: parseInt(e.target.value) || 0 })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="0"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Countries (comma-separated)
              </label>
              <input
                type="text"
                value={formData.countries}
                onChange={(e) => setFormData({ ...formData, countries: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="e.g., Canada, United States"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Provinces/States (comma-separated)
              </label>
              <input
                type="text"
                value={formData.provinces || formData.states}
                onChange={(e) => setFormData({ ...formData, provinces: e.target.value, states: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="e.g., Ontario, California"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Minimum Order Amount ($)
              </label>
              <input
                type="number"
                min="0"
                step="0.01"
                value={formData.minimumOrderAmount}
                onChange={(e) => setFormData({ ...formData, minimumOrderAmount: parseFloat(e.target.value) || 0 })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Maximum Order Amount ($)
              </label>
              <input
                type="number"
                min="0"
                step="0.01"
                value={formData.maximumOrderAmount}
                onChange={(e) => setFormData({ ...formData, maximumOrderAmount: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Optional"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Applicable Categories (comma-separated)
            </label>
            <input
              type="text"
              value={formData.applicableCategories}
              onChange={(e) => setFormData({ ...formData, applicableCategories: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="e.g., Electronics, Clothing (leave empty for all categories)"
            />
          </div>

          <div className="flex items-center">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={formData.isActive}
                onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="ml-2 text-sm text-gray-700">Active</span>
            </label>
          </div>

          <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={saving}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
            >
              {saving ? 'Saving...' : (taxRule ? 'Update' : 'Create')} Tax Rule
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default TaxManagement;
