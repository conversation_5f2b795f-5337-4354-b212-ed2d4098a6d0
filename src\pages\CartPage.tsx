import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { Plus, Minus, Trash2, ShoppingBag, ArrowLeft, CreditCard } from 'lucide-react';
import { useCart } from '../context/CartContext';
import { useToast } from '../context/ToastContext';
import { ConfirmDialog } from '../components/ConfirmDialog';
import { getProductImageUrl, handleImageError } from '../utils/imageUtils';

export function CartPage() {
  const { items, removeItem, updateQuantity, clearCart, totalPrice, totalItems } = useCart();
  const { showToast } = useToast();
  const [showClearConfirm, setShowClearConfirm] = useState(false);
  const [showRemoveConfirm, setShowRemoveConfirm] = useState(false);
  const [itemToRemove, setItemToRemove] = useState<string | null>(null);

  const handleQuantityChange = (id: string, newQuantity: number) => {
    if (newQuantity <= 0) {
      setItemToRemove(id);
      setShowRemoveConfirm(true);
    } else {
      updateQuantity(id, newQuantity);
      showToast('Quantity updated', 'success');
    }
  };

  const handleRemoveItem = (id: string) => {
    setItemToRemove(id);
    setShowRemoveConfirm(true);
  };

  const confirmRemoveItem = () => {
    if (itemToRemove) {
      removeItem(itemToRemove);
      showToast('Item removed from cart', 'success');
      setItemToRemove(null);
    }
    setShowRemoveConfirm(false);
  };

  const handleClearCart = () => {
    if (items.length === 0) {
      showToast('Cart is already empty', 'info');
      return;
    }
    setShowClearConfirm(true);
  };

  const confirmClearCart = () => {
    clearCart();
    showToast('Cart cleared successfully', 'success');
    setShowClearConfirm(false);
  };

  if (items.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">Shopping Cart</h1>
            <p className="text-lg text-gray-600">Your cart is currently empty</p>
          </div>

          {/* Empty Cart */}
          <div className="max-w-md mx-auto text-center">
            <div className="bg-white rounded-3xl shadow-lg p-12 border border-gray-100">
              <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <ShoppingBag className="h-12 w-12 text-gray-400" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Your cart is empty</h3>
              <p className="text-gray-600 mb-8">
                Looks like you haven't added any items to your cart yet.
              </p>
              <Link
                to="/shop"
                className="inline-flex items-center space-x-2 bg-gradient-to-r from-green-600 to-green-700 text-white px-8 py-3 rounded-2xl hover:from-green-700 hover:to-green-800 transition-all duration-300 font-semibold shadow-xl hover:shadow-2xl transform hover:scale-105"
              >
                <span>Start Shopping</span>
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-12">
          <div>
            <h1 className="text-4xl font-bold text-gray-900 mb-4">Shopping Cart</h1>
            <p className="text-lg text-gray-600">{totalItems} {totalItems === 1 ? 'item' : 'items'} in your cart</p>
          </div>
          <Link
            to="/shop"
            className="inline-flex items-center space-x-2 text-green-600 hover:text-green-700 font-semibold transition-colors"
          >
            <ArrowLeft className="h-5 w-5" />
            <span>Continue Shopping</span>
          </Link>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Cart Items */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-3xl shadow-lg border border-gray-100 overflow-hidden">
              <div className="p-6 border-b border-gray-100 bg-gradient-to-r from-green-50 to-emerald-50">
                <div className="flex items-center justify-between">
                  <div>
                    <h2 className="text-2xl font-bold text-gray-900 mb-1">Your Items</h2>
                    <p className="text-sm text-gray-600">Review and modify your cart items</p>
                  </div>
                  {items.length > 0 && (
                    <button
                      onClick={handleClearCart}
                      className="bg-red-100 text-red-700 hover:bg-red-200 hover:text-red-800 font-medium text-sm px-4 py-2 rounded-xl transition-colors"
                    >
                      Clear All
                    </button>
                  )}
                </div>
              </div>

              <div className="divide-y divide-gray-100">
                {items.map((item, index) => (
                  <div key={item.id} className="p-6 hover:bg-gray-50/50 transition-all duration-200">
                    <div className="flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-6">
                      {/* Product Image */}
                      <div className="flex-shrink-0">
                        <img
                          src={getProductImageUrl(item.product)}
                          alt={item.product.name}
                          onError={handleImageError}
                          className="w-24 h-24 sm:w-28 sm:h-28 object-cover rounded-2xl border border-gray-200 shadow-sm"
                        />
                      </div>

                      {/* Product Info */}
                      <div className="flex-1 min-w-0">
                        <h3 className="text-xl font-bold text-gray-900 mb-2">
                          {item.product.name}
                        </h3>
                        <p className="text-sm text-green-600 font-medium mb-3 bg-green-50 px-3 py-1 rounded-full inline-block">
                          {item.product.category}
                        </p>
                        <div className="flex items-center space-x-3">
                          <span className="text-2xl font-bold text-gray-900">${item.product.price}</span>
                          {item.product.originalPrice && (
                            <div className="flex flex-col">
                              <span className="text-sm text-gray-500 line-through">
                                ${item.product.originalPrice}
                              </span>
                              <span className="text-xs text-green-600 font-semibold">
                                Save ${(item.product.originalPrice - item.product.price).toFixed(2)}
                              </span>
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Quantity and Actions */}
                      <div className="flex flex-col sm:flex-row items-start sm:items-center space-y-4 sm:space-y-0 sm:space-x-4">
                        {/* Quantity Controls */}
                        <div className="flex items-center">
                          <span className="text-sm font-medium text-gray-700 mr-3">Qty:</span>
                          <div className="flex items-center bg-gray-100 rounded-xl">
                            <button
                              onClick={() => handleQuantityChange(item.id, item.quantity - 1)}
                              className="p-3 hover:bg-gray-200 rounded-l-xl transition-colors"
                              disabled={item.quantity <= 1}
                            >
                              <Minus className="h-4 w-4 text-gray-600" />
                            </button>
                            <span className="px-4 py-3 font-bold text-gray-900 min-w-[3rem] text-center">
                              {item.quantity}
                            </span>
                            <button
                              onClick={() => handleQuantityChange(item.id, item.quantity + 1)}
                              className="p-3 hover:bg-gray-200 rounded-r-xl transition-colors"
                            >
                              <Plus className="h-4 w-4 text-gray-600" />
                            </button>
                          </div>
                        </div>

                        {/* Item Total */}
                        <div className="text-right">
                          <p className="text-sm text-gray-600 mb-1">Item Total</p>
                          <p className="text-xl font-bold text-gray-900">
                            ${(item.product.price * item.quantity).toFixed(2)}
                          </p>
                        </div>

                        {/* Remove Button */}
                        <button
                          onClick={() => handleRemoveItem(item.id)}
                          className="p-3 text-red-600 hover:text-red-700 hover:bg-red-50 rounded-xl transition-colors"
                          title="Remove item"
                        >
                          <Trash2 className="h-5 w-5" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Order Summary */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-3xl shadow-lg border border-gray-100 p-6 sticky top-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-6">Order Summary</h2>

              <div className="space-y-4 mb-6">
                <div className="flex justify-between text-gray-600">
                  <span>Subtotal ({totalItems} items)</span>
                  <span>${totalPrice.toFixed(2)}</span>
                </div>
                <div className="flex justify-between text-gray-600">
                  <span>Shipping</span>
                  <span className="text-green-600 font-medium">Free</span>
                </div>
                <div className="flex justify-between text-gray-600">
                  <span>Tax</span>
                  <span>${(totalPrice * 0.08).toFixed(2)}</span>
                </div>
                <div className="border-t border-gray-200 pt-4">
                  <div className="flex justify-between text-lg font-bold text-gray-900">
                    <span>Total</span>
                    <span>${(totalPrice * 1.08).toFixed(2)}</span>
                  </div>
                </div>
              </div>

              <div className="space-y-3">
                <Link
                  to="/checkout"
                  className="w-full bg-gradient-to-r from-green-600 to-green-700 text-white px-6 py-4 rounded-2xl hover:from-green-700 hover:to-green-800 transition-all duration-300 font-semibold shadow-xl hover:shadow-2xl transform hover:scale-105 flex items-center justify-center space-x-2"
                >
                  <CreditCard className="h-5 w-5" />
                  <span>Proceed to Checkout</span>
                </Link>
                
                <Link
                  to="/shop"
                  className="w-full bg-gray-100 text-gray-700 px-6 py-4 rounded-2xl hover:bg-gray-200 transition-all duration-300 font-semibold text-center block"
                >
                  Continue Shopping
                </Link>
              </div>

              {/* Security Badge */}
              <div className="mt-6 pt-6 border-t border-gray-200">
                <div className="flex items-center justify-center space-x-2 text-sm text-gray-600">
                  <div className="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-xs">✓</span>
                  </div>
                  <span>Secure checkout guaranteed</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Confirmation Dialogs */}
      <ConfirmDialog
        isOpen={showClearConfirm}
        title="Clear Cart"
        message="Are you sure you want to remove all items from your cart? This action cannot be undone."
        confirmText="Clear Cart"
        cancelText="Cancel"
        onConfirm={confirmClearCart}
        onCancel={() => setShowClearConfirm(false)}
        type="danger"
      />

      <ConfirmDialog
        isOpen={showRemoveConfirm}
        title="Remove Item"
        message="Are you sure you want to remove this item from your cart?"
        confirmText="Remove"
        cancelText="Cancel"
        onConfirm={confirmRemoveItem}
        onCancel={() => setShowRemoveConfirm(false)}
        type="warning"
      />
    </div>
  );
}
