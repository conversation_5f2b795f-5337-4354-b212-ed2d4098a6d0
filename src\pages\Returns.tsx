import React from 'react';
import { Link } from 'react-router-dom';
import {
  RotateCcw,
  CheckCircle,
  XCircle,
  Clock,
  RefreshCw,
  Mail,
  Phone,
  Package,
  CreditCard,
  Truck,
  AlertCircle
} from 'lucide-react';

export function Returns() {
  const returnSteps = [
    {
      step: 1,
      title: 'Contact Us',
      description: 'Reach out to our customer service team within 30 days of purchase',
      icon: <Mail className="h-6 w-6" />,
      color: 'from-blue-500 to-blue-600'
    },
    {
      step: 2,
      title: 'Get Return Label',
      description: 'We\'ll email you a prepaid return shipping label and instructions',
      icon: <Package className="h-6 w-6" />,
      color: 'from-green-500 to-green-600'
    },
    {
      step: 3,
      title: 'Pack & Ship',
      description: 'Pack your item securely and drop it off at any shipping location',
      icon: <Truck className="h-6 w-6" />,
      color: 'from-orange-500 to-orange-600'
    },
    {
      step: 4,
      title: 'Get Refund',
      description: 'Receive your refund within 5-7 business days after we receive your item',
      icon: <CreditCard className="h-6 w-6" />,
      color: 'from-purple-500 to-purple-600'
    }
  ];

  const returnConditions = [
    {
      icon: CheckCircle,
      title: 'Returnable Items',
      items: [
        'Unused items in original packaging',
        'Items with all tags attached',
        'Items returned within 30 days',
        'Items in original condition'
      ]
    },
    {
      icon: XCircle,
      title: 'Non-Returnable Items',
      items: [
        'Personalized or customized items',
        'Items damaged by misuse',
        'Items without original packaging',
        'Final sale items'
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="py-12 lg:py-16 bg-gradient-to-br from-green-600 to-emerald-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-white/20 rounded-2xl mb-6">
            <RotateCcw className="h-8 w-8" />
          </div>
          <h1 className="text-4xl lg:text-5xl font-bold mb-6">Return Policy</h1>
          <p className="text-xl text-green-100 max-w-3xl mx-auto leading-relaxed">
            Not completely satisfied? No problem! We offer hassle-free returns within 30 days of purchase with full refunds.
          </p>
          <div className="mt-6 text-sm text-green-200">
            Last updated: {new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })}
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="py-16 lg:py-20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Introduction */}
          <div className="bg-white rounded-3xl shadow-lg p-8 lg:p-10 mb-12 border border-gray-100">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Our Return Promise</h2>
            <div className="prose prose-lg text-gray-700 leading-relaxed">
              <p className="mb-4">
                At jaisalgoonline, your satisfaction is our top priority. We stand behind the quality of our products
                and want you to be completely happy with your purchase. If for any reason you're not satisfied,
                we offer a hassle-free 30-day return policy.
              </p>
              <p className="mb-4">
                We believe shopping online should be risk-free. That's why we've made our return process as simple
                and straightforward as possible. No complicated forms, no hidden fees, just easy returns.
              </p>
              <p>
                This return policy applies to all purchases made through our website. Please read the details below
                to understand the return process and conditions.
              </p>
            </div>
          </div>

          {/* Return Process */}
          <div className="bg-white rounded-3xl shadow-lg p-8 lg:p-10 mb-12 border border-gray-100">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">How to Return Your Order</h2>
              <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                Follow these simple steps to return your item and get your refund processed quickly.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {returnSteps.map((step, index) => (
                <div key={index} className="relative">
                  {index < returnSteps.length - 1 && (
                    <div className="hidden lg:block absolute top-12 left-full w-full h-0.5 bg-gray-200 transform translate-x-4 -translate-y-1/2"></div>
                  )}
                  <div className="text-center">
                    <div className={`inline-flex p-4 rounded-2xl bg-gradient-to-r ${step.color} text-white mb-6 relative`}>
                      {step.icon}
                      <div className="absolute -top-2 -right-2 w-8 h-8 bg-gray-900 text-white rounded-full flex items-center justify-center text-sm font-bold">
                        {step.step}
                      </div>
                    </div>
                    <h3 className="text-xl font-bold text-gray-900 mb-3">{step.title}</h3>
                    <p className="text-gray-600 leading-relaxed">{step.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Return Conditions */}
          <div className="bg-white rounded-3xl shadow-lg p-8 lg:p-10 mb-12 border border-gray-100">
            <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">Return Conditions</h2>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {returnConditions.map((condition, index) => (
                <div key={index} className="bg-gray-50 rounded-2xl p-6 border border-gray-100">
                  <div className="flex items-center mb-6">
                    <condition.icon className={`h-8 w-8 mr-3 ${
                      condition.title === 'Returnable Items' ? 'text-green-500' : 'text-red-500'
                    }`} />
                    <h3 className="text-xl font-semibold text-gray-900">{condition.title}</h3>
                  </div>
                  <ul className="space-y-3">
                    {condition.items.map((item, itemIndex) => (
                      <li key={itemIndex} className="flex items-start">
                        <span className={`inline-block w-2 h-2 rounded-full mt-2 mr-3 ${
                          condition.title === 'Returnable Items' ? 'bg-green-500' : 'bg-red-500'
                        }`}></span>
                        <span className="text-gray-600">{item}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>

          {/* Additional Information */}
          <div className="bg-white rounded-3xl shadow-lg p-8 lg:p-10 mb-12 border border-gray-100">
            <h2 className="text-2xl font-bold text-gray-900 mb-8">Additional Information</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                  <RotateCcw className="h-6 w-6 mr-2 text-green-600" />
                  Exchanges
                </h3>
                <p className="text-gray-700 leading-relaxed">
                  We currently don't offer direct exchanges. To exchange an item, please return the
                  original item for a refund and place a new order for the desired item.
                </p>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                  <Clock className="h-6 w-6 mr-2 text-green-600" />
                  Refund Timeline
                </h3>
                <p className="text-gray-700 leading-relaxed">
                  Refunds are processed within 5-7 business days after we receive your returned item.
                  The refund will be credited to your original payment method.
                </p>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                  <Truck className="h-6 w-6 mr-2 text-green-600" />
                  Return Shipping
                </h3>
                <p className="text-gray-700 leading-relaxed">
                  We provide prepaid return shipping labels for all returns within the United States.
                  For international returns, customers are responsible for return shipping costs.
                </p>
              </div>
            </div>
          </div>

          {/* Contact Section */}
          <div className="bg-gradient-to-br from-green-50 to-emerald-50 rounded-3xl shadow-lg p-8 lg:p-10 border border-green-100">
            <div className="text-center mb-8">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-green-600 text-white rounded-2xl mb-6">
                <RefreshCw className="h-8 w-8" />
              </div>
              <h2 className="text-2xl font-bold text-gray-900 mb-4">Need Help with Your Return?</h2>
              <p className="text-gray-700 leading-relaxed max-w-2xl mx-auto">
                Our customer service team is here to make your return process as smooth as possible.
                Contact us and we'll guide you through every step.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="inline-flex items-center justify-center w-12 h-12 bg-green-600 text-white rounded-2xl mb-4">
                  <Mail className="h-6 w-6" />
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">Email</h3>
                <a
                  href="mailto:<EMAIL>"
                  className="text-green-600 hover:text-green-700 transition-colors font-medium"
                >
                  <EMAIL>
                </a>
              </div>

              <div className="text-center">
                <div className="inline-flex items-center justify-center w-12 h-12 bg-green-600 text-white rounded-2xl mb-4">
                  <Phone className="h-6 w-6" />
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">Phone</h3>
                <a
                  href="tel:+15551234567"
                  className="text-green-600 hover:text-green-700 transition-colors font-medium"
                >
                  +****************
                </a>
              </div>

              <div className="text-center">
                <div className="inline-flex items-center justify-center w-12 h-12 bg-green-600 text-white rounded-2xl mb-4">
                  <AlertCircle className="h-6 w-6" />
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">Support</h3>
                <Link
                  to="/support"
                  className="text-green-600 hover:text-green-700 transition-colors font-medium"
                >
                  Visit Support Center
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
