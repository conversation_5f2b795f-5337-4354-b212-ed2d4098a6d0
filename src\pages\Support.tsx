import React from 'react';
import { Link } from 'react-router-dom';
import { 
  MessageCircle, 
  Mail, 
  Phone, 
  Clock, 
  HelpCircle, 
  Package, 
  CreditCard, 
  RefreshCw, 
  Shield, 
  Truck,
  FileText,
  Users,
  Search,
  ArrowRight
} from 'lucide-react';

export function Support() {
  const supportCategories = [
    {
      icon: <HelpCircle className="h-8 w-8" />,
      title: 'Frequently Asked Questions',
      description: 'Find quick answers to common questions about orders, shipping, and returns.',
      link: '/faq',
      color: 'from-blue-500 to-blue-600'
    },
    {
      icon: <Package className="h-8 w-8" />,
      title: 'Order & Shipping',
      description: 'Track your orders, learn about shipping options, and delivery information.',
      link: '/shipping-info',
      color: 'from-green-500 to-green-600'
    },
    {
      icon: <RefreshCw className="h-8 w-8" />,
      title: 'Returns & Exchanges',
      description: 'Easy returns process, exchange policies, and refund information.',
      link: '/returns',
      color: 'from-orange-500 to-orange-600'
    },
    {
      icon: <CreditCard className="h-8 w-8" />,
      title: 'Payment & Billing',
      description: 'Payment methods, billing questions, and account management.',
      link: '/contact',
      color: 'from-purple-500 to-purple-600'
    },
    {
      icon: <Shield className="h-8 w-8" />,
      title: 'Privacy & Security',
      description: 'Learn about our privacy policy, data protection, and security measures.',
      link: '/privacy-policy',
      color: 'from-red-500 to-red-600'
    },
    {
      icon: <FileText className="h-8 w-8" />,
      title: 'Terms & Policies',
      description: 'Terms of service, return policy, and other important policies.',
      link: '/terms-of-service',
      color: 'from-indigo-500 to-indigo-600'
    }
  ];

  const contactMethods = [
    {
      icon: <MessageCircle className="h-6 w-6" />,
      title: 'Live Chat',
      description: 'Chat with our support team in real-time',
      availability: 'Available 24/7',
      action: 'Start Chat',
      primary: true
    },
    {
      icon: <Mail className="h-6 w-6" />,
      title: 'Email Support',
      description: 'Send us an email and we\'ll respond within 24 hours',
      availability: '<EMAIL>',
      action: 'Send Email',
      primary: false
    },
    {
      icon: <Phone className="h-6 w-6" />,
      title: 'Phone Support',
      description: 'Speak directly with our customer service team',
      availability: 'Mon-Fri, 9AM-6PM EST',
      action: 'Call Now',
      primary: false
    }
  ];

  const quickActions = [
    { icon: <Search className="h-5 w-5" />, title: 'Track Order', link: '/account' },
    { icon: <Package className="h-5 w-5" />, title: 'Return Item', link: '/returns' },
    { icon: <CreditCard className="h-5 w-5" />, title: 'Payment Issues', link: '/contact' },
    { icon: <Users className="h-5 w-5" />, title: 'Account Help', link: '/account' }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-br from-green-900 via-green-800 to-green-900 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 sm:py-24 lg:py-32">
          <div className="text-center">
            <div className="inline-flex items-center px-6 py-3 rounded-full bg-white/10 backdrop-blur-md border border-white/20 mb-8">
              <MessageCircle className="h-5 w-5 mr-3 text-green-300" />
              <span className="text-sm font-semibold">Customer Support Center</span>
            </div>
            <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold mb-6">
              How Can We Help You?
            </h1>
            <p className="text-lg sm:text-xl md:text-2xl text-green-100 max-w-4xl mx-auto leading-relaxed mb-8">
              Get the support you need with our comprehensive help center, live chat, and expert customer service team.
            </p>
            
            {/* Quick Actions */}
            <div className="flex flex-wrap justify-center gap-4 mt-8">
              {quickActions.map((action, index) => (
                <Link
                  key={index}
                  to={action.link}
                  className="inline-flex items-center px-6 py-3 bg-white/10 backdrop-blur-md border border-white/20 rounded-xl text-white hover:bg-white/20 transition-all duration-300 transform hover:scale-105"
                >
                  {action.icon}
                  <span className="ml-2 font-medium">{action.title}</span>
                </Link>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Contact Methods */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Get In Touch</h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Choose the best way to reach us. Our customer service team is ready to help you with any questions or concerns.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {contactMethods.map((method, index) => (
              <div
                key={index}
                className={`relative p-8 rounded-2xl border-2 transition-all duration-300 hover:shadow-xl transform hover:-translate-y-1 ${
                  method.primary 
                    ? 'border-green-200 bg-gradient-to-br from-green-50 to-emerald-50' 
                    : 'border-gray-200 bg-white hover:border-green-200'
                }`}
              >
                {method.primary && (
                  <div className="absolute -top-3 left-6 bg-green-600 text-white px-4 py-1 rounded-full text-sm font-semibold">
                    Recommended
                  </div>
                )}
                <div className={`inline-flex p-3 rounded-xl mb-4 ${
                  method.primary ? 'bg-green-600 text-white' : 'bg-gray-100 text-gray-600'
                }`}>
                  {method.icon}
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-2">{method.title}</h3>
                <p className="text-gray-600 mb-4">{method.description}</p>
                <p className="text-sm text-gray-500 mb-6">{method.availability}</p>
                <button className={`w-full px-6 py-3 rounded-xl font-semibold transition-all duration-300 ${
                  method.primary
                    ? 'bg-green-600 text-white hover:bg-green-700 shadow-lg hover:shadow-xl'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}>
                  {method.action}
                </button>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Support Categories */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Browse Help Topics</h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Find detailed information and step-by-step guides for all your questions.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {supportCategories.map((category, index) => (
              <Link
                key={index}
                to={category.link}
                className="group bg-white p-8 rounded-2xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
              >
                <div className={`inline-flex p-4 rounded-2xl bg-gradient-to-r ${category.color} text-white mb-6 group-hover:scale-110 transition-transform duration-300`}>
                  {category.icon}
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-green-600 transition-colors">
                  {category.title}
                </h3>
                <p className="text-gray-600 mb-4 leading-relaxed">
                  {category.description}
                </p>
                <div className="flex items-center text-green-600 font-medium group-hover:translate-x-2 transition-transform duration-300">
                  <span>Learn More</span>
                  <ArrowRight className="h-4 w-4 ml-2" />
                </div>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Business Hours */}
      <section className="py-16 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-gradient-to-r from-green-600 to-emerald-600 rounded-2xl p-8 text-white text-center">
            <Clock className="h-12 w-12 mx-auto mb-4" />
            <h2 className="text-2xl font-bold mb-4">Customer Service Hours</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-left max-w-2xl mx-auto">
              <div>
                <h3 className="font-semibold mb-2">Phone & Live Chat</h3>
                <p className="text-green-100">Monday - Friday: 9:00 AM - 6:00 PM EST</p>
                <p className="text-green-100">Saturday: 10:00 AM - 4:00 PM EST</p>
                <p className="text-green-100">Sunday: Closed</p>
              </div>
              <div>
                <h3 className="font-semibold mb-2">Email Support</h3>
                <p className="text-green-100">Available 24/7</p>
                <p className="text-green-100">Response within 24 hours</p>
                <p className="text-green-100">Priority support for urgent issues</p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
