# Deployment Guide for Your E-commerce Project

## Current Issue
Your frontend is deployed on Vercel but trying to connect to `localhost:4000` which doesn't exist in production. The backend needs to be deployed as well.

## Solution: Deploy Backend as Vercel Serverless Functions

### Step 1: Set Environment Variables in Vercel Dashboard

1. Go to your Vercel dashboard: https://vercel.com/dashboard
2. Select your project
3. Go to Settings → Environment Variables
4. Add these environment variables:

```
VITE_STRIPE_PUBLISHABLE_KEY = pk_test_51QyG87FsgADt7MeasKUX0hR7wVWgYHIyxc58RnykgSLw1B4Hgkfna9TGmwxVSkuuaSoiIkUJocm6KPFy3V6tfpdK00uEEKF2Zk
VITE_STRIPE_SECRET_KEY = sk_test_51QyG87FsgADt7MeaXmHQyvQYGcjE71yuRGZv0xx0Le8WlZPY9O6rSTnrQOmURCeHc9udXjpF2r28voLB52z3Gv6700T4vpEd6w
MONGODB_URI = mongodb://sulta4567:<EMAIL>:27017,cluster0-shard-00-01.c9q8b.mongodb.net:27017,cluster0-shard-00-02.c9q8b.mongodb.net:27017/jaisalgoonline?replicaSet=atlas-v49dz3-shard-0&ssl=true&authSource=admin
JWT_SECRET = your_jwt_secret_key_here
BLOB_READ_WRITE_TOKEN = a94wAAm68ve0dsabgkUF00QL
```

**Important**: Make sure to set these for all environments (Production, Preview, Development)

### Step 2: Deploy the Updated Code

1. Commit your changes:
```bash
git add .
git commit -m "Configure for Vercel deployment with serverless functions"
git push
```

2. Vercel will automatically redeploy your project

### Step 3: Test the Deployment

After deployment, your API endpoints will be available at:
- `https://your-domain.vercel.app/api/products`
- `https://your-domain.vercel.app/api/users`
- etc.

## What We Changed

1. **Updated `vercel.json`**: 
   - Added serverless function configuration
   - Added API route rewrites
   - Added environment variables

2. **Updated `src/utils/env.ts`**:
   - Made API_URL dynamic to use current domain in production

3. **Existing `api/index.js`**:
   - Already configured as a Vercel serverless function

## Alternative Solution: Deploy Backend Separately

If you prefer to deploy the backend separately (e.g., on Railway, Render, or Heroku):

1. Deploy your `server/` folder to a hosting service
2. Update the `VITE_API_URL` environment variable in Vercel to point to your backend URL
3. Make sure CORS is configured to allow your frontend domain

## Troubleshooting

If you still get connection errors after deployment:

1. Check Vercel Function Logs:
   - Go to your Vercel dashboard
   - Click on your project
   - Go to Functions tab
   - Check the logs for any errors

2. Verify Environment Variables:
   - Make sure all environment variables are set correctly
   - Check that MongoDB connection string is accessible from Vercel

3. Test API endpoints directly:
   - Try accessing `https://your-domain.vercel.app/api/health` in your browser
   - Should return a health check response

## Next Steps

1. Set the environment variables in Vercel dashboard
2. Push the code changes
3. Test your deployed application
4. If issues persist, check the Vercel function logs for specific errors
