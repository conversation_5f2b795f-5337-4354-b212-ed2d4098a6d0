# Testing Guide for New E-commerce Features

## 🧪 Quick Test Plan

### Step 1: Run Database Migration
```bash
cd server
npm run migrate
```

### Step 2: Test Admin Features

#### A. Refund Management (`/admin/refunds`)
1. **Access**: Go to Admin Panel → Refunds
2. **Expected**: See refund management interface
3. **Test**: Filter by status, view refund details

#### B. Discount Management (`/admin/discounts`)
1. **Access**: Go to Admin Panel → Discounts
2. **Expected**: See existing sample discounts (WELCOME10, FREESHIP)
3. **Test**: 
   - Create new discount code
   - Edit existing discount
   - Test different discount types

#### C. Tax Management (`/admin/taxes`)
1. **Access**: Go to Admin Panel → Tax Rules
2. **Expected**: See sample tax rules for Canada/US
3. **Test**:
   - Create new tax rule
   - Edit existing rules
   - Test different tax types

### Step 3: Test Customer Features

#### A. Refund Requests
1. **Prerequisites**: Have at least one completed order
2. **Access**: Go to `/refund/:orderId` (replace with actual order ID)
3. **Test**:
   - Select items to refund
   - Choose refund reasons
   - Submit refund request
   - View refund status at `/refund`

#### B. Enhanced Product Display
1. **Access**: Go to Shop page
2. **Expected**: Products now show brands instead of "Unknown"
3. **Test**: Check product details for new fields

### Step 4: API Testing

#### Test Discount Validation
```javascript
// In browser console on any page
fetch('/api/discounts/validate', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${localStorage.getItem('token')}`
  },
  body: JSON.stringify({
    code: 'WELCOME10',
    orderAmount: 100,
    items: []
  })
}).then(r => r.json()).then(console.log);
```

#### Test Tax Calculation
```javascript
// In browser console
fetch('/api/taxes/calculate', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${localStorage.getItem('token')}`
  },
  body: JSON.stringify({
    orderAmount: 100,
    items: [],
    shippingAddress: {
      country: 'Canada',
      province: 'Ontario'
    }
  })
}).then(r => r.json()).then(console.log);
```

## ✅ Expected Results

### After Migration:
- ✅ Products have proper brand names (no more "Unknown")
- ✅ Orders have order numbers
- ✅ Sample tax rules created
- ✅ Sample discount codes created

### Admin Panel:
- ✅ New navigation items: Refunds, Discounts, Tax Rules
- ✅ Functional management interfaces
- ✅ Create, edit, delete operations work

### Customer Experience:
- ✅ Can request refunds for orders
- ✅ Can view refund status
- ✅ Enhanced product information

### Analytics:
- ✅ Brand data shows properly (no more "Unknown" in charts)
- ✅ Enhanced product performance data

## 🐛 Troubleshooting

### Common Issues:

#### 1. Migration Fails
**Problem**: Database connection error
**Solution**: Check MONGODB_URI in .env file

#### 2. Admin Routes Not Working
**Problem**: 404 errors on new admin routes
**Solution**: Restart the frontend development server

#### 3. API Endpoints Return 404
**Problem**: Backend routes not registered
**Solution**: Restart the backend server

#### 4. Still Seeing "Unknown" Brands
**Problem**: Migration didn't run or failed
**Solution**: 
- Check migration logs
- Run migration again
- Manually update products in admin panel

#### 5. Refund Requests Fail
**Problem**: Order not found or permission denied
**Solution**: 
- Ensure order belongs to logged-in user
- Check order status (must be delivered/processing)

## 🔍 Debug Commands

### Check Migration Status:
```bash
# In MongoDB shell or Compass
db.products.findOne({}, {brand: 1, sku: 1})
db.orders.findOne({}, {orderNumber: 1, discountAmount: 1})
db.taxrules.count()
db.discounts.count()
```

### Check API Health:
```bash
# Test if new routes are working
curl -X GET http://localhost:5000/api/discounts
curl -X GET http://localhost:5000/api/taxes
curl -X GET http://localhost:5000/api/refunds/admin
```

## 📊 Success Metrics

After successful implementation, you should see:

### Analytics Dashboard:
- ✅ Proper brand distribution (no "Unknown" at 100%)
- ✅ Enhanced product performance data
- ✅ Tax and refund analytics

### Admin Efficiency:
- ✅ Easy refund management workflow
- ✅ Flexible discount campaign creation
- ✅ Comprehensive tax rule configuration

### Customer Satisfaction:
- ✅ Self-service refund requests
- ✅ Clear product information with brands
- ✅ Transparent pricing with tax breakdown

## 🎯 Next Steps After Testing

1. **Configure Tax Rules**: Set up tax rules for your specific regions
2. **Create Discount Campaigns**: Design promotional campaigns
3. **Train Staff**: Familiarize admin users with new features
4. **Monitor Analytics**: Track refund rates and discount usage
5. **Customer Communication**: Inform customers about new refund process

## 🚀 Production Deployment

Before going live:
1. ✅ Test all features thoroughly
2. ✅ Configure production tax rules
3. ✅ Set up monitoring for refund requests
4. ✅ Train customer service team
5. ✅ Update terms of service for refund policy

Your e-commerce platform is now enterprise-ready! 🎉
