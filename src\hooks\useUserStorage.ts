import { useEffect, useState } from 'react';

/**
 * Hook for managing user-specific localStorage data
 */
export function useUserStorage<T>(
  key: string,
  defaultValue: T,
  userId?: string
): [T, (value: T) => void] {
  const [storedValue, setStoredValue] = useState<T>(defaultValue);

  // Get the storage key based on user ID
  const getStorageKey = (baseKey: string, userId?: string) => {
    return userId ? `${baseKey}_${userId}` : baseKey;
  };

  // Load data from localStorage when userId changes
  useEffect(() => {
    try {
      const storageKey = getStorageKey(key, userId);
      const item = localStorage.getItem(storageKey);
      if (item) {
        setStoredValue(JSON.parse(item));
      } else {
        setStoredValue(defaultValue);
      }
    } catch (error) {
      console.error(`Error loading ${key} from localStorage:`, error);
      setStoredValue(defaultValue);
    }
  }, [key, userId, defaultValue]);

  // Function to update the stored value
  const setValue = (value: T) => {
    try {
      setStoredValue(value);
      const storageKey = getStorageKey(key, userId);
      localStorage.setItem(storageKey, JSON.stringify(value));
    } catch (error) {
      console.error(`Error saving ${key} to localStorage:`, error);
    }
  };

  return [storedValue, setValue];
}

/**
 * Hook for managing user-specific cart data
 */
export function useUserCart(userId?: string) {
  return useUserStorage('cart', { items: [] }, userId);
}

/**
 * Hook for managing user-specific wishlist data
 */
export function useUserWishlist(userId?: string) {
  return useUserStorage('wishlist', [], userId);
}
