import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Star, ShoppingCart, Eye, Heart } from 'lucide-react';
import { Product } from '../types';
import { useCart } from '../context/CartContext';
import { useWishlist } from '../context/WishlistContext';
import { useToast } from '../context/ToastContext';
import { getProductImageUrl, handleImageError } from '../utils/imageUtils';

interface ProductCardProps {
  product: Product;
  onViewDetails?: (product: Product) => void;
}

export function ProductCard({ product, onViewDetails }: ProductCardProps) {
  const navigate = useNavigate();
  const { addItem } = useCart();
  const { addToWishlist, removeFromWishlist, isInWishlist } = useWishlist();
  const { showToast } = useToast();

  // State for real-time updates
  const [productRating, setProductRating] = React.useState(product.rating || 0);
  const [reviewCount, setReviewCount] = React.useState(product.reviews || 0);
  const [pricingInfo, setPricingInfo] = React.useState<any>(null);

  // Initialize pricing info from product data
  React.useEffect(() => {
    if ((product as any).pricingInfo) {
      setPricingInfo((product as any).pricingInfo);
    } else {
      // Fallback pricing calculation if server doesn't provide it
      setPricingInfo({
        originalPrice: product.originalPrice || product.price,
        effectivePrice: product.price,
        hasDiscount: false,
        discountPercentage: 0,
        savingsAmount: 0
      });
    }
  }, [product]);

  // Listen for real-time review updates
  React.useEffect(() => {
    const handleProductReviewAdded = (event: CustomEvent) => {
      const { productId, newRating, reviewCount: newReviewCount } = event.detail;

      if (productId === product.id) {
        console.log('📊 ProductCard updating rating:', { productId, newRating, newReviewCount });
        setProductRating(newRating || 0);
        setReviewCount(newReviewCount || 0);
      }
    };

    const handleProductRatingUpdated = (event: CustomEvent) => {
      const { productId, rating, reviews } = event.detail;

      if (productId === product.id) {
        console.log('📊 ProductCard updating from rating event:', { productId, rating, reviews });
        setProductRating(rating || 0);
        setReviewCount(reviews || 0);
      }
    };

    // Add event listeners
    document.addEventListener('productReviewAdded', handleProductReviewAdded as EventListener);
    document.addEventListener('productRatingUpdated', handleProductRatingUpdated as EventListener);

    // Cleanup
    return () => {
      document.removeEventListener('productReviewAdded', handleProductReviewAdded as EventListener);
      document.removeEventListener('productRatingUpdated', handleProductRatingUpdated as EventListener);
    };
  }, [product.id]);

  const handleAddToCart = (e: React.MouseEvent) => {
    e.stopPropagation();
    addItem(product);
    showToast('Added to cart 🛒', 'success');
  };

  const handleViewDetails = () => {
    if (onViewDetails) {
      onViewDetails(product);
    } else {
      navigate(`/product/${product.id}`);
    }
  };

  const handleWishlistToggle = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (isInWishlist(product.id)) {
      removeFromWishlist(product.id);
      showToast('Removed from wishlist', 'success');
    } else {
      addToWishlist(product);
      showToast('Added to wishlist ❤️', 'success');
    }
  };

  return (
    <div
      onClick={handleViewDetails}
      className="group relative bg-white rounded-2xl shadow-lg border border-green-100 overflow-hidden hover:shadow-xl hover:shadow-green-500/10 transition-all duration-300 transform hover:-translate-y-1 h-full flex flex-col cursor-pointer">
      {/* Product Image */}
      <div className="relative aspect-square overflow-hidden bg-gradient-to-br from-gray-50 to-gray-100">
        <img
          src={getProductImageUrl(product)}
          alt={product.name}
          onError={handleImageError}
          className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
        />

        {/* Overlay */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
        
        {/* Wishlist Button - Always Visible */}
        <button
          onClick={handleWishlistToggle}
          className={`absolute top-3 right-3 p-2 rounded-full transition-all duration-300 shadow-lg z-10 transform hover:scale-110 ${
            isInWishlist(product.id)
              ? 'bg-red-500 text-white hover:bg-red-600 animate-pulse'
              : 'bg-white/90 text-gray-600 hover:bg-white hover:text-red-500'
          }`}
          title={isInWishlist(product.id) ? 'Remove from wishlist' : 'Add to wishlist'}
        >
          <Heart className={`h-4 w-4 ${isInWishlist(product.id) ? 'fill-current' : ''}`} />
        </button>

        {/* Overlay Actions */}
        <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center space-x-3">
          <button
            onClick={handleViewDetails}
            className="bg-white/90 backdrop-blur-sm p-3 rounded-full hover:bg-white transition-colors shadow-lg"
          >
            <Eye className="h-5 w-5 text-gray-700" />
          </button>
          <button
            onClick={handleAddToCart}
            className="bg-green-600 backdrop-blur-sm text-white p-3 rounded-full hover:bg-green-700 transition-colors shadow-lg"
          >
            <ShoppingCart className="h-5 w-5" />
          </button>
        </div>

        {/* Enhanced Badges */}
        {pricingInfo?.hasDiscount && pricingInfo.discountPercentage > 0 && (
          <div className="absolute top-3 left-3 bg-red-500 text-white px-3 py-1 rounded-full text-xs font-bold shadow-lg animate-pulse">
            {pricingInfo.discountPercentage}% OFF
          </div>
        )}

        {product.featured && (
          <div className="absolute top-12 left-3 bg-green-600 text-white px-3 py-1 rounded-full text-xs font-bold shadow-lg">
            ⭐ FEATURED
          </div>
        )}
        
        {!product.inStock && (
          <div className="absolute bottom-3 left-3 bg-gray-600 text-white px-3 py-1 rounded-full text-xs font-bold shadow-lg">
            OUT OF STOCK
          </div>
        )}

        {product.inStock && product.quantity !== undefined && product.quantity <= 5 && (
          <div className="absolute bottom-3 left-3 bg-orange-500 text-white px-3 py-1 rounded-full text-xs font-bold shadow-lg">
            ONLY {product.quantity} LEFT
          </div>
        )}
      </div>

      {/* Product Info */}
      <div className="p-4 flex-1 flex flex-col">
        <div className="mb-2">
          <span className="text-xs text-green-700 font-bold uppercase tracking-wider bg-green-100 px-2 py-1 rounded-full">{product.category}</span>
        </div>

        <h3 className="text-sm font-bold text-gray-900 mb-2 line-clamp-2 group-hover:text-green-600 transition-colors leading-tight">
          {product.name}
        </h3>

        {/* Rating */}
        <div className="flex items-center mb-2">
          <div className="flex items-center space-x-1">
            {[...Array(5)].map((_, i) => (
              <Star
                key={i}
                className={`h-2.5 w-2.5 ${
                  i < Math.floor(productRating)
                    ? 'text-yellow-400 fill-current'
                    : 'text-gray-300'
                }`}
              />
            ))}
          </div>
          <span className="ml-2 text-xs text-green-700 font-semibold bg-green-100 px-2 py-1 rounded-full">
            {productRating.toFixed(1)} ({reviewCount})
          </span>
        </div>

        {/* Enhanced Price Display */}
        <div className="mt-auto">
          <div className="flex items-center justify-between mb-3">
            <div className="flex flex-col space-y-1">
              {/* Current Price and Original Price */}
              <div className="flex items-center space-x-2">
                <span className="text-lg font-bold text-gray-900">
                  ${product.price.toFixed(2)}
                </span>
                {product.originalPrice && product.originalPrice > product.price && (
                  <span className="text-sm text-gray-500 line-through font-medium">
                    ${product.originalPrice.toFixed(2)}
                  </span>
                )}
              </div>

              {/* Savings Information */}
              {product.originalPrice && product.originalPrice > product.price && (
                <div className="flex flex-col space-y-1">
                  <span className="text-xs text-green-600 font-semibold bg-green-50 px-2 py-1 rounded-full">
                    💰 Save ${(product.originalPrice - product.price).toFixed(2)} ({Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)}% OFF)
                  </span>
                </div>
              )}

              {/* Tax Information */}
              {product.taxRate && product.taxRate > 0 && (
                <span className="text-xs text-blue-600 font-medium">
                  {product.taxIncluded ? 'Tax included' : `+${product.taxRate}% tax`}
                </span>
              )}
            </div>
          </div>

          <button
            onClick={handleAddToCart}
            disabled={!product.inStock}
            className={`w-full px-3 py-2 rounded-lg transition-all duration-300 font-semibold text-xs shadow-lg hover:shadow-xl transform hover:scale-105 ${product.inStock
              ? 'bg-green-600 text-white hover:bg-green-700'
              : 'bg-gray-300 text-gray-500 cursor-not-allowed'}`}
          >
            {product.inStock ? 'Add to Cart' : 'Out of Stock'}
          </button>
        </div>
      </div>
    </div>
  );
}