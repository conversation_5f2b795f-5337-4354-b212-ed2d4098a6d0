import { Link } from 'react-router-dom';
import { Mail, Phone, Info, Github, ExternalLink, Home } from 'lucide-react';

export function AdminFooter() {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-gradient-to-br from-green-800 to-emerald-900 text-white mt-auto">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              <img
                src="/jaisal-go-online-high-resolution-logo.png"
                alt="jaisalgoonline"
                className="h-12 w-auto object-contain rounded-[5px]"
              />
              <div>
                <span className="text-xl font-bold">jaisalgoonline</span>
                <p className="text-sm text-green-200">Admin Dashboard</p>
              </div>
            </div>
            <p className="text-green-100 leading-relaxed">
              Comprehensive admin dashboard for managing your e-commerce store with powerful tools and analytics.
            </p>
          </div>

          {/* Admin Navigation */}
          <div>
            <h3 className="text-lg font-semibold mb-4 text-white">Admin Navigation</h3>
            <ul className="space-y-3">
              <li><Link to="/admin" className="text-green-200 hover:text-white transition-colors flex items-center"><span className="mr-2">📊</span>Dashboard</Link></li>
              <li><Link to="/admin/products" className="text-green-200 hover:text-white transition-colors flex items-center"><span className="mr-2">📦</span>Products</Link></li>
              <li><Link to="/admin/orders" className="text-green-200 hover:text-white transition-colors flex items-center"><span className="mr-2">🛒</span>Orders</Link></li>
              <li><Link to="/admin/users" className="text-green-200 hover:text-white transition-colors flex items-center"><span className="mr-2">👥</span>Users</Link></li>
              <li><Link to="/" className="text-green-200 hover:text-white transition-colors flex items-center"><Home className="h-4 w-4 mr-2" />View Store</Link></li>
            </ul>
          </div>

          {/* Quick Actions */}
          <div>
            <h3 className="text-lg font-semibold mb-4 text-white">Quick Actions</h3>
            <ul className="space-y-3">
              <li className="flex items-center text-green-200">
                <Info className="h-4 w-4 mr-2 text-green-300" />
                <span>System v1.0.0</span>
              </li>
              <li><Link to="/account" className="text-green-200 hover:text-white transition-colors flex items-center"><ExternalLink className="h-4 w-4 mr-2" />My Account</Link></li>
              <li><Link to="/privacy-policy" className="text-green-200 hover:text-white transition-colors">Privacy Policy</Link></li>
              <li><Link to="/terms-of-service" className="text-green-200 hover:text-white transition-colors">Terms of Service</Link></li>
            </ul>
          </div>

          {/* Support & Contact */}
          <div>
            <h3 className="text-lg font-semibold mb-4 text-white">Support & Contact</h3>
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <Mail className="h-5 w-5 text-green-300" />
                <a href="mailto:<EMAIL>" className="text-green-200 hover:text-white transition-colors"><EMAIL></a>
              </div>
              <div className="flex items-center space-x-3">
                <Phone className="h-5 w-5 text-green-300" />
                <a href="tel:+***********" className="text-green-200 hover:text-white transition-colors">+****************</a>
              </div>
              <div className="flex items-center space-x-3">
                <Github className="h-5 w-5 text-green-300" />
                <a href="#" className="text-green-200 hover:text-white transition-colors">Documentation</a>
              </div>
            </div>
          </div>
        </div>

        <div className="border-t border-green-700 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
          <div className="flex flex-col md:flex-row items-center space-y-2 md:space-y-0 md:space-x-4">
            <p className="text-green-200 text-sm">
              © {currentYear} jaisalgoonline. All rights reserved.
            </p>
            <span className="hidden md:block text-green-300">•</span>
            <p className="text-green-300 text-sm font-medium">
              Admin Dashboard v1.0.0
            </p>
          </div>
          <div className="flex space-x-6 mt-4 md:mt-0">
            <Link to="/" className="text-green-200 hover:text-white text-sm transition-colors flex items-center">
              <Home className="h-4 w-4 mr-1" />
              Return to Store
            </Link>
            <Link to="/account" className="text-green-200 hover:text-white text-sm transition-colors">My Account</Link>
          </div>
        </div>
      </div>
    </footer>
  );
}

export default AdminFooter;