const mongoose = require('mongoose');

const DiscountSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  code: {
    type: String,
    required: true,
    unique: true,
    uppercase: true,
    trim: true
  },
  description: String,
  type: {
    type: String,
    enum: ['percentage', 'fixed_amount', 'buy_x_get_y', 'free_shipping'],
    required: true
  },
  value: {
    type: Number,
    required: true,
    min: 0
  },
  minimumOrderAmount: {
    type: Number,
    default: 0,
    min: 0
  },
  maximumDiscountAmount: {
    type: Number,
    min: 0
  },
  applicableProducts: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Product'
  }],
  applicableCategories: [String],
  excludedProducts: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Product'
  }],
  usageLimit: {
    total: {
      type: Number,
      min: 1
    },
    perUser: {
      type: Number,
      min: 1,
      default: 1
    }
  },
  usageCount: {
    type: Number,
    default: 0,
    min: 0
  },
  userUsage: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    count: {
      type: Number,
      default: 0
    },
    lastUsed: Date
  }],
  validFrom: {
    type: Date,
    required: true
  },
  validUntil: {
    type: Date,
    required: true
  },
  isActive: {
    type: Boolean,
    default: true
  },
  isPublic: {
    type: Boolean,
    default: true
  },
  // Buy X Get Y specific fields
  buyQuantity: Number,
  getQuantity: Number,
  getDiscountPercentage: Number,
  // Free shipping conditions
  freeShippingThreshold: Number,
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

DiscountSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Method to check if discount is valid
DiscountSchema.methods.isValid = function() {
  const now = new Date();
  return this.isActive && 
         this.validFrom <= now && 
         this.validUntil >= now &&
         (!this.usageLimit.total || this.usageCount < this.usageLimit.total);
};

// Method to check if user can use this discount
DiscountSchema.methods.canUserUse = function(userId) {
  if (!this.isValid()) return false;
  
  const userUsage = this.userUsage.find(u => u.user.toString() === userId.toString());
  if (!userUsage) return true;
  
  return userUsage.count < this.usageLimit.perUser;
};

module.exports = mongoose.model('Discount', DiscountSchema);
