import { Shield, Eye, Lock, Users, Database, Mail, Phone, MapPin } from 'lucide-react';

export function PrivacyPolicy() {
  const sections = [
    {
      id: 'information-collection',
      title: 'Information We Collect',
      icon: Database,
      content: [
        {
          subtitle: 'Personal Information',
          text: 'We collect information you provide directly to us, such as when you create an account, make a purchase, subscribe to our newsletter, or contact us. This may include your name, email address, phone number, shipping address, billing address, and payment information.'
        },
        {
          subtitle: 'Automatically Collected Information',
          text: 'We automatically collect certain information about your device and usage of our website, including your IP address, browser type, operating system, referring URLs, pages viewed, and the dates/times of visits.'
        },
        {
          subtitle: 'Cookies and Tracking Technologies',
          text: 'We use cookies, web beacons, and similar tracking technologies to collect information about your browsing activities and to provide personalized content and advertisements.'
        }
      ]
    },
    {
      id: 'information-use',
      title: 'How We Use Your Information',
      icon: Eye,
      content: [
        {
          subtitle: 'Service Provision',
          text: 'We use your information to process orders, provide customer service, send order confirmations and shipping notifications, and communicate with you about your account or transactions.'
        },
        {
          subtitle: 'Improvement and Personalization',
          text: 'We analyze usage patterns to improve our website, products, and services. We also use your information to personalize your shopping experience and provide relevant product recommendations.'
        },
        {
          subtitle: 'Marketing Communications',
          text: 'With your consent, we may send you promotional emails about new products, special offers, and other information we think you may find interesting. You can opt out at any time.'
        }
      ]
    },
    {
      id: 'information-sharing',
      title: 'Information Sharing and Disclosure',
      icon: Users,
      content: [
        {
          subtitle: 'Service Providers',
          text: 'We may share your information with third-party service providers who perform services on our behalf, such as payment processing, order fulfillment, email delivery, and website analytics.'
        },
        {
          subtitle: 'Legal Requirements',
          text: 'We may disclose your information if required to do so by law or if we believe that such disclosure is necessary to protect our rights, protect your safety or the safety of others, investigate fraud, or respond to government requests.'
        },
        {
          subtitle: 'Business Transfers',
          text: 'In the event of a merger, acquisition, or sale of assets, your information may be transferred as part of that transaction. We will notify you of any such change in ownership or control.'
        }
      ]
    },
    {
      id: 'data-security',
      title: 'Data Security',
      icon: Lock,
      content: [
        {
          subtitle: 'Security Measures',
          text: 'We implement appropriate technical and organizational security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction.'
        },
        {
          subtitle: 'Payment Security',
          text: 'All payment transactions are processed through secure, encrypted connections. We do not store your complete credit card information on our servers.'
        },
        {
          subtitle: 'Data Retention',
          text: 'We retain your personal information for as long as necessary to fulfill the purposes outlined in this privacy policy, unless a longer retention period is required or permitted by law.'
        }
      ]
    }
  ];

  const contactInfo = [
    {
      icon: Mail,
      label: 'Email',
      value: '<EMAIL>',
      link: 'mailto:<EMAIL>'
    },
    {
      icon: Phone,
      label: 'Phone',
      value: '+****************',
      link: 'tel:+***********'
    },
    {
      icon: MapPin,
      label: 'Address',
      value: '123 Commerce Street, Business District, NY 10001',
      link: null
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="py-12 lg:py-16 bg-gradient-to-br from-green-600 to-emerald-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-white/20 rounded-2xl mb-6">
            <Shield className="h-8 w-8" />
          </div>
          <h1 className="text-4xl lg:text-5xl font-bold mb-6">Privacy Policy</h1>
          <p className="text-xl text-green-100 max-w-3xl mx-auto leading-relaxed">
            Your privacy is important to us. This policy explains how we collect, use, and protect your personal information.
          </p>
          <div className="mt-6 text-sm text-green-200">
            Last updated: {new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })}
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="py-16 lg:py-20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Introduction */}
          <div className="bg-white rounded-3xl shadow-lg p-8 lg:p-10 mb-12 border border-gray-100">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Introduction</h2>
            <div className="prose prose-lg text-gray-700 leading-relaxed">
              <p className="mb-4">
                At jaisalgoonline, we are committed to protecting your privacy and ensuring the security of your personal information.
                This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you visit our website
                or make a purchase from us.
              </p>
              <p className="mb-4">
                By using our website, you consent to the data practices described in this policy. If you do not agree with the
                practices described in this policy, please do not use our website.
              </p>
              <p>
                We may update this Privacy Policy from time to time. We will notify you of any changes by posting the new
                Privacy Policy on this page and updating the "Last updated" date.
              </p>
            </div>
          </div>

          {/* Privacy Sections */}
          <div className="space-y-8">
            {sections.map((section, index) => (
              <div key={section.id} className="bg-white rounded-3xl shadow-lg overflow-hidden border border-gray-100">
                <div className="p-8 lg:p-10">
                  <div className="flex items-center mb-6">
                    <div className="inline-flex items-center justify-center w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-2xl mr-4">
                      <section.icon className="h-6 w-6" />
                    </div>
                    <h2 className="text-2xl font-bold text-gray-900">{section.title}</h2>
                  </div>

                  <div className="space-y-6">
                    {section.content.map((item, itemIndex) => (
                      <div key={itemIndex}>
                        <h3 className="text-lg font-semibold text-gray-900 mb-3">{item.subtitle}</h3>
                        <p className="text-gray-700 leading-relaxed">{item.text}</p>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Your Rights Section */}
          <div className="bg-white rounded-3xl shadow-lg p-8 lg:p-10 mt-12 border border-gray-100">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Your Rights and Choices</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Access and Update</h3>
                  <p className="text-gray-700">You can access and update your account information at any time by logging into your account.</p>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Email Preferences</h3>
                  <p className="text-gray-700">You can opt out of promotional emails by clicking the unsubscribe link in any email or updating your preferences in your account.</p>
                </div>
              </div>
              <div className="space-y-4">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Data Deletion</h3>
                  <p className="text-gray-700">You can request deletion of your personal information by contacting us. Some information may be retained for legal or business purposes.</p>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Cookie Settings</h3>
                  <p className="text-gray-700">You can control cookies through your browser settings, though this may affect website functionality.</p>
                </div>
              </div>
            </div>
          </div>

          {/* Contact Section */}
          <div className="bg-gradient-to-br from-green-50 to-emerald-50 rounded-3xl shadow-lg p-8 lg:p-10 mt-12 border border-green-100">
            <div className="text-center mb-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">Questions About Privacy?</h2>
              <p className="text-gray-700 leading-relaxed">
                If you have any questions about this Privacy Policy or our data practices, please don't hesitate to contact us.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {contactInfo.map((contact, index) => (
                <div key={index} className="text-center">
                  <div className="inline-flex items-center justify-center w-12 h-12 bg-green-600 text-white rounded-2xl mb-4">
                    <contact.icon className="h-6 w-6" />
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2">{contact.label}</h3>
                  {contact.link ? (
                    <a
                      href={contact.link}
                      className="text-green-600 hover:text-green-700 transition-colors font-medium"
                    >
                      {contact.value}
                    </a>
                  ) : (
                    <p className="text-gray-700">{contact.value}</p>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Additional Information */}
          <div className="bg-white rounded-3xl shadow-lg p-8 lg:p-10 mt-12 border border-gray-100">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Additional Information</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Children's Privacy</h3>
                <p className="text-gray-700 leading-relaxed">
                  Our website is not intended for children under 13 years of age. We do not knowingly collect personal
                  information from children under 13. If we learn that we have collected personal information from a
                  child under 13, we will delete that information as quickly as possible.
                </p>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">International Users</h3>
                <p className="text-gray-700 leading-relaxed">
                  If you are accessing our website from outside the United States, please be aware that your information
                  may be transferred to, stored, and processed in the United States where our servers are located.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
