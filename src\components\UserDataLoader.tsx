import { useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import { useCart } from '../context/CartContext';
import { useWishlist } from '../context/WishlistContext';

/**
 * Component that handles loading user-specific data when authentication state changes
 */
export function UserDataLoader() {
  try {
    const { user, isAuthenticated } = useAuth();
    const { loadUserCart } = useCart();
    const { loadUserWishlist } = useWishlist();

    useEffect(() => {
      try {
        if (isAuthenticated && user && user._id) {
          // Load user-specific cart and wishlist data
          loadUserCart(user._id);
          loadUserWishlist(user._id);
        }
      } catch (error) {
        console.error('Error loading user data:', error);
      }
    }, [isAuthenticated, user, loadUserCart, loadUserWishlist]);

    // This component doesn't render anything
    return null;
  } catch (error) {
    console.error('Error in UserDataLoader:', error);
    return null;
  }
}
