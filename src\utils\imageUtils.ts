/**
 * Get a reliable image URL with fallback options
 */
export const getProductImageUrl = (product: { image: string; images?: string[] }): string => {
  // Try the main image first
  if (product.image && product.image.trim() !== '') {
    return product.image;
  }
  
  // Try the first image from images array
  if (product.images && product.images.length > 0 && product.images[0].trim() !== '') {
    return product.images[0];
  }
  
  // Fallback to a placeholder image
  return '/placeholder-product.svg';
};

/**
 * Get all available images for a product
 */
export const getProductImages = (product: { image: string; images?: string[] }): string[] => {
  const allImages: string[] = [];
  
  // Add main image if it exists
  if (product.image && product.image.trim() !== '') {
    allImages.push(product.image);
  }
  
  // Add additional images if they exist
  if (product.images && product.images.length > 0) {
    product.images.forEach(img => {
      if (img && img.trim() !== '' && !allImages.includes(img)) {
        allImages.push(img);
      }
    });
  }
  
  // If no images found, return placeholder
  if (allImages.length === 0) {
    allImages.push('/placeholder-product.svg');
  }
  
  return allImages;
};

/**
 * Handle image load errors by setting a fallback
 */
export const handleImageError = (event: React.SyntheticEvent<HTMLImageElement>) => {
  const img = event.currentTarget;
  if (img.src !== '/placeholder-product.svg') {
    img.src = '/placeholder-product.svg';
  }
};
