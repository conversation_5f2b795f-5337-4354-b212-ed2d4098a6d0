const mongoose = require('mongoose');

const ReviewSchema = new mongoose.Schema({
  product: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Product',
    required: [true, 'Product ID is required']
  },
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'User ID is required']
  },
  rating: {
    type: Number,
    required: [true, 'Rating is required'],
    min: 1,
    max: 5
  },
  title: {
    type: String,
    required: [true, 'Review title is required'],
    trim: true,
    maxlength: [100, 'Title cannot be more than 100 characters']
  },
  comment: {
    type: String,
    required: [true, 'Review comment is required'],
    trim: true,
    maxlength: [1000, 'Comment cannot be more than 1000 characters']
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Update the updatedAt field on save
ReviewSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Static method to calculate average rating for a product
ReviewSchema.statics.calculateAverageRating = async function(productId) {
  try {
    console.log('🔄 Calculating average rating for product:', productId);

    // Ensure we have a valid ObjectId
    const mongoose = require('mongoose');
    let objectId;

    if (mongoose.Types.ObjectId.isValid(productId)) {
      objectId = new mongoose.Types.ObjectId(productId);
    } else {
      console.error('❌ Invalid product ID:', productId);
      return;
    }

    // Get all reviews for this product
    const reviews = await this.find({ product: objectId });
    console.log(`📊 Found ${reviews.length} reviews for product ${productId}`);

    let averageRating = 0;
    let numReviews = reviews.length;

    if (numReviews > 0) {
      const totalRating = reviews.reduce((sum, review) => sum + review.rating, 0);
      averageRating = totalRating / numReviews;
      console.log(`📈 Calculated rating: ${averageRating} from ${numReviews} reviews`);
    }

    // Update the product with new rating and review count
    const Product = mongoose.model('Product');
    const updatedProduct = await Product.findByIdAndUpdate(
      objectId,
      {
        rating: Math.round(averageRating * 10) / 10, // Round to 1 decimal place
        reviews: numReviews,
        updatedAt: new Date()
      },
      { new: true, runValidators: true }
    );

    if (updatedProduct) {
      console.log('✅ Product rating updated successfully:', {
        id: updatedProduct._id,
        name: updatedProduct.name,
        rating: updatedProduct.rating,
        reviews: updatedProduct.reviews
      });

      return updatedProduct;
    } else {
      console.error('❌ Failed to update product rating');
      throw new Error('Failed to update product rating');
    }
  } catch (error) {
    console.error('❌ Error calculating average rating:', error);
    throw error;
  }
};

// Call calculateAverageRating after save
ReviewSchema.post('save', async function(doc) {
  console.log('🔄 Review saved, triggering rating calculation...');
  try {
    await this.constructor.calculateAverageRating(doc.product);
  } catch (error) {
    console.error('❌ Error in post-save hook:', error);
  }
});

// Call calculateAverageRating after remove
ReviewSchema.post('remove', async function(doc) {
  console.log('🔄 Review removed, triggering rating calculation...');
  try {
    await this.constructor.calculateAverageRating(doc.product);
  } catch (error) {
    console.error('❌ Error in post-remove hook:', error);
  }
});

// Also handle deleteOne and deleteMany
ReviewSchema.post('deleteOne', async function() {
  console.log('🔄 Review deleted (deleteOne), triggering rating calculation...');
  const doc = await this.model.findOne(this.getQuery());
  if (doc) {
    try {
      await this.model.calculateAverageRating(doc.product);
    } catch (error) {
      console.error('❌ Error in post-deleteOne hook:', error);
    }
  }
});

ReviewSchema.post('findOneAndDelete', async function(doc) {
  if (doc) {
    console.log('🔄 Review deleted (findOneAndDelete), triggering rating calculation...');
    try {
      await this.model.calculateAverageRating(doc.product);
    } catch (error) {
      console.error('❌ Error in post-findOneAndDelete hook:', error);
    }
  }
});

module.exports = mongoose.model('Review', ReviewSchema);