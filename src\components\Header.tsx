import { useState } from 'react';
import { Search, Menu, X, Heart, User, ShoppingCart } from 'lucide-react';
import { Link, useLocation } from 'react-router-dom';

import { useCart } from '../context/CartContext';
import { useWishlist } from '../context/WishlistContext';

interface HeaderProps {
  onSearch: (query: string) => void;
  searchQuery: string;
}

export function Header({ onSearch, searchQuery }: HeaderProps) {
  const { totalItems } = useCart();
  const { wishlistCount } = useWishlist();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const location = useLocation();

  const navigation = [
    { name: 'Home', href: '/' },
    { name: 'Shop', href: '/shop' },
    { name: 'About', href: '/about' },
    { name: 'Contact', href: '/contact' }
  ];

  const isActive = (path: string) => location.pathname === path;

  return (
    <header className="sticky top-0 z-50 bg-green-700 shadow-lg">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <div className="flex items-center">
            <Link to="/" className="flex-shrink-0 flex items-center group">
              <img
                src="/jaisal-go-online-high-resolution-logo.png"
                alt="jaisalgoonline"
                className="h-14 w-auto object-contain group-hover:scale-105 transition-transform duration-200 rounded-[5px]"
              />
            </Link>
          </div>

          {/* Navigation - Desktop */}
          <nav className="hidden lg:flex space-x-1">
            {navigation.map((item) => (
              <Link
                key={item.name}
                to={item.href}
                className={`px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 ${
                  isActive(item.href)
                    ? 'bg-green-600 text-white shadow-md'
                    : 'text-white hover:bg-green-600 hover:text-white'
                }`}
              >
                {item.name}
              </Link>
            ))}
          </nav>

          {/* Search Bar - Desktop */}
          <div className="hidden lg:flex flex-1 max-w-md mx-6">
            <div className="relative w-full">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-4 w-4 text-green-400" />
              </div>
              <input
                type="text"
                placeholder="Search products..."
                value={searchQuery}
                onChange={(e) => onSearch(e.target.value)}
                className="block w-full pl-10 pr-4 py-2 border border-green-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200 text-sm"
              />
            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center space-x-2">
            {/* Search Icon - Mobile */}
            <button className="lg:hidden p-2 text-white hover:text-green-200 transition-colors rounded-lg hover:bg-green-600">
              <Search className="h-5 w-5" />
            </button>

            {/* Wishlist */}
            <Link to="/wishlist" className="hidden sm:block relative p-2 text-white hover:text-green-200 transition-colors rounded-lg hover:bg-green-600">
              <Heart className="h-5 w-5" />
              {wishlistCount > 0 && (
                <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-medium animate-pulse">
                  {wishlistCount}
                </span>
              )}
            </Link>

            {/* Cart */}
            <Link
              to="/cart"
              className="relative p-2 text-white hover:text-green-200 transition-colors group rounded-lg hover:bg-green-600"
            >
              <ShoppingCart className="h-5 w-5 group-hover:scale-110 transition-transform" />
              {totalItems > 0 && (
                <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-medium animate-pulse">
                  {totalItems}
                </span>
              )}
            </Link>

            {/* Account */}
            <Link to="/account" className="hidden sm:block p-2 text-white hover:text-green-200 transition-colors rounded-lg hover:bg-green-600">
              <User className="h-5 w-5" />
            </Link>

            {/* Mobile Menu Button */}
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="lg:hidden p-2 text-white hover:text-green-200 transition-colors rounded-lg hover:bg-green-600"
            >
              {isMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="lg:hidden py-4 border-t border-green-600 bg-green-700">
            <div className="space-y-4">
              {/* Mobile Navigation */}
              <nav className="space-y-1">
                {navigation.map((item) => (
                  <Link
                    key={item.name}
                    to={item.href}
                    onClick={() => setIsMenuOpen(false)}
                    className={`block px-4 py-3 text-base font-medium transition-colors rounded-lg mx-2 ${
                      isActive(item.href)
                        ? 'text-green-700 bg-white'
                        : 'text-white hover:text-green-700 hover:bg-white'
                    }`}
                  >
                    {item.name}
                  </Link>
                ))}
              </nav>

              <div className="relative mx-2">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Search className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  placeholder="Search products..."
                  value={searchQuery}
                  onChange={(e) => onSearch(e.target.value)}
                  className="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                />
              </div>
              <div className="flex flex-col space-y-2 mx-2">
                <Link to="/cart" className="flex items-center space-x-3 text-white hover:text-green-200 px-4 py-3 rounded-lg hover:bg-green-600 transition-colors" onClick={() => setIsMenuOpen(false)}>
                  <ShoppingCart className="h-5 w-5" />
                  <span>Cart ({totalItems})</span>
                </Link>
                <Link to="/wishlist" className="flex items-center space-x-3 text-white hover:text-green-200 px-4 py-3 rounded-lg hover:bg-green-600 transition-colors" onClick={() => setIsMenuOpen(false)}>
                  <Heart className="h-5 w-5" />
                  <span>Wishlist ({wishlistCount})</span>
                </Link>
                <Link to="/account" className="flex items-center space-x-3 text-white hover:text-green-200 px-4 py-3 rounded-lg hover:bg-green-600 transition-colors" onClick={() => setIsMenuOpen(false)}>
                  <User className="h-5 w-5" />
                  <span>Account</span>
                </Link>
              </div>
            </div>
          </div>
        )}
      </div>
    </header>
  );
}