import React from 'react';
import { Link } from 'react-router-dom';
import {
  Truck,
  Clock,
  MapPin,
  Package,
  Shield,
  Globe,
  CreditCard,
  CheckCircle,
  AlertCircle,
  Plane,
  DollarSign
} from 'lucide-react';

export function ShippingInfo() {
  const shippingOptions = [
    {
      name: 'Standard Shipping',
      timeframe: '5-7 Business Days',
      cost: 'Free on orders $50+',
      description: 'Reliable ground shipping for most orders',
      icon: <Truck className="h-6 w-6" />,
      color: 'from-blue-500 to-blue-600',
      popular: false
    },
    {
      name: 'Express Shipping',
      timeframe: '2-3 Business Days',
      cost: '$9.99',
      description: 'Faster delivery for urgent orders',
      icon: <Plane className="h-6 w-6" />,
      color: 'from-green-500 to-green-600',
      popular: true
    },
    {
      name: 'Overnight Shipping',
      timeframe: '1 Business Day',
      cost: '$19.99',
      description: 'Next-day delivery for time-sensitive items',
      icon: <Clock className="h-6 w-6" />,
      color: 'from-orange-500 to-orange-600',
      popular: false
    },
    {
      name: 'International Shipping',
      timeframe: '7-14 Business Days',
      cost: 'Calculated at checkout',
      description: 'Worldwide delivery to most countries',
      icon: <Globe className="h-6 w-6" />,
      color: 'from-purple-500 to-purple-600',
      popular: false
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-br from-green-900 via-green-800 to-green-900 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 sm:py-24 lg:py-32">
          <div className="text-center">
            <div className="inline-flex items-center px-6 py-3 rounded-full bg-white/10 backdrop-blur-md border border-white/20 mb-8">
              <Truck className="h-5 w-5 mr-3 text-green-300" />
              <span className="text-sm font-semibold">Shipping Information</span>
            </div>
            <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold mb-6">
              Fast & Reliable Shipping
            </h1>
            <p className="text-lg sm:text-xl md:text-2xl text-green-100 max-w-4xl mx-auto leading-relaxed">
              We deliver your orders quickly and safely with multiple shipping options to meet your needs.
            </p>
          </div>
        </div>
      </div>

      {/* Shipping Options */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Shipping Options</h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Choose the shipping method that works best for you.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {shippingOptions.map((option, index) => (
              <div
                key={index}
                className={`relative p-6 rounded-2xl border-2 transition-all duration-300 hover:shadow-xl transform hover:-translate-y-1 ${
                  option.popular
                    ? 'border-green-200 bg-gradient-to-br from-green-50 to-emerald-50'
                    : 'border-gray-200 bg-white hover:border-green-200'
                }`}
              >
                {option.popular && (
                  <div className="absolute -top-3 left-6 bg-green-600 text-white px-4 py-1 rounded-full text-sm font-semibold">
                    Most Popular
                  </div>
                )}
                <div className={`inline-flex p-3 rounded-xl mb-4 bg-gradient-to-r ${option.color} text-white`}>
                  {option.icon}
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-2">{option.name}</h3>
                <div className="mb-3">
                  <div className="flex items-center space-x-2 mb-1">
                    <Clock className="h-4 w-4 text-gray-500" />
                    <span className="text-sm font-medium text-gray-700">{option.timeframe}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <CreditCard className="h-4 w-4 text-gray-500" />
                    <span className="text-sm font-medium text-green-600">{option.cost}</span>
                  </div>
                </div>
                <p className="text-gray-600 text-sm">{option.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Shipping Features */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Why Choose Our Shipping?</h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              We go above and beyond to ensure your packages arrive safely and on time.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="inline-flex p-4 rounded-2xl bg-gradient-to-r from-green-500 to-emerald-500 text-white mb-6 transform hover:scale-110 transition-transform duration-300">
                <DollarSign className="h-6 w-6" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-3">Free Shipping Threshold</h3>
              <p className="text-gray-600 leading-relaxed">Get free standard shipping on all orders over $50</p>
            </div>
            <div className="text-center">
              <div className="inline-flex p-4 rounded-2xl bg-gradient-to-r from-blue-500 to-cyan-500 text-white mb-6 transform hover:scale-110 transition-transform duration-300">
                <MapPin className="h-6 w-6" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-3">Order Tracking</h3>
              <p className="text-gray-600 leading-relaxed">Track your package in real-time with detailed updates</p>
            </div>
            <div className="text-center">
              <div className="inline-flex p-4 rounded-2xl bg-gradient-to-r from-purple-500 to-violet-500 text-white mb-6 transform hover:scale-110 transition-transform duration-300">
                <Shield className="h-6 w-6" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-3">Secure Packaging</h3>
              <p className="text-gray-600 leading-relaxed">All items are carefully packaged to prevent damage</p>
            </div>
            <div className="text-center">
              <div className="inline-flex p-4 rounded-2xl bg-gradient-to-r from-orange-500 to-red-500 text-white mb-6 transform hover:scale-110 transition-transform duration-300">
                <Package className="h-6 w-6" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-3">Insurance Included</h3>
              <p className="text-gray-600 leading-relaxed">All shipments include insurance coverage</p>
            </div>
          </div>
        </div>
      </section>

      {/* Shipping Details */}
      <section className="py-16 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">Shipping Details</h2>
          
          <div className="space-y-8">
            <div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Processing Time</h3>
              <p className="text-gray-600">
                Orders are typically processed within 1-2 business days. Orders placed on weekends 
                or holidays will be processed the next business day.
              </p>
            </div>

            <div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Shipping Restrictions</h3>
              <ul className="list-disc pl-6 text-gray-600 space-y-2">
                <li>We currently ship to all 50 US states and most international locations</li>
                <li>Some items may have shipping restrictions due to size or regulations</li>
                <li>PO Boxes are accepted for standard shipping only</li>
                <li>Expedited shipping is not available to PO Boxes</li>
              </ul>
            </div>

            <div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Order Tracking</h3>
              <p className="text-gray-600">
                Once your order ships, you'll receive a tracking number via email. You can track 
                your package on our website or directly with the shipping carrier.
              </p>
            </div>

            <div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Delivery Issues</h3>
              <p className="text-gray-600">
                If your package is lost or damaged during shipping, please contact us immediately. 
                We'll work with the carrier to resolve the issue and ensure you receive your order.
              </p>
            </div>
          </div>

          <div className="mt-12 bg-gradient-to-r from-amber-50 to-orange-50 border border-amber-200 rounded-2xl p-8">
            <div className="flex items-start space-x-4">
              <AlertCircle className="h-8 w-8 text-amber-600 flex-shrink-0 mt-1" />
              <div>
                <h3 className="text-xl font-bold text-amber-900 mb-4">Important Shipping Information</h3>
                <ul className="space-y-3 text-amber-800">
                  <li className="flex items-start space-x-2">
                    <span className="w-2 h-2 bg-amber-600 rounded-full mt-2 flex-shrink-0"></span>
                    <span>Orders placed before 2 PM EST ship the same business day</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <span className="w-2 h-2 bg-amber-600 rounded-full mt-2 flex-shrink-0"></span>
                    <span>Shipping times are estimates and may vary during peak seasons</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <span className="w-2 h-2 bg-amber-600 rounded-full mt-2 flex-shrink-0"></span>
                    <span>International orders may be subject to customs duties and taxes</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <span className="w-2 h-2 bg-amber-600 rounded-full mt-2 flex-shrink-0"></span>
                    <span>We do not ship to P.O. boxes for express and overnight delivery</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <span className="w-2 h-2 bg-amber-600 rounded-full mt-2 flex-shrink-0"></span>
                    <span>Tracking information is provided via email once your order ships</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-gradient-to-r from-green-600 to-emerald-600 rounded-2xl p-8 text-white text-center">
            <div className="inline-flex p-4 bg-white/10 rounded-2xl mb-6">
              <Truck className="h-8 w-8" />
            </div>
            <h2 className="text-3xl font-bold mb-4">Questions About Shipping?</h2>
            <p className="text-lg text-green-100 mb-8 max-w-2xl mx-auto">
              Our customer service team is here to help with any shipping questions or concerns.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                to="/contact"
                className="bg-white text-green-600 px-8 py-4 rounded-xl font-semibold hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 shadow-lg inline-flex items-center justify-center"
              >
                <Package className="h-5 w-5 mr-2" />
                Track Your Order
              </Link>
              <Link
                to="/support"
                className="border-2 border-white text-white px-8 py-4 rounded-xl font-semibold hover:bg-white hover:text-green-600 transition-all duration-300 transform hover:scale-105 inline-flex items-center justify-center"
              >
                <Clock className="h-5 w-5 mr-2" />
                Get Support
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
